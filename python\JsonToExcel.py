from queue import Full
import tkinter as tk
from tkinter import ttk
from tkinter import filedialog
from datetime import datetime
import os
import requests
import json
import sqlite3
import pandas as pd
import time
import hashlib
import base64
import schedule
import threading
from tkcalendar import DateEntry  # 导入 DateEntry

def print_response(response):
    print("响应内容:", response.text)

def insert_records(cursor, records, device_id):
    # 解析每条记录
    parsed_records = [record.split(',') for record in records]
    print(f"插入记录 (device_id: {device_id}):", parsed_records)

    # 检查每条记录是否已经存在于数据库中
    for record in parsed_records:
        cursor.execute('''
            SELECT COUNT(*) FROM records 
            WHERE device_id = ? AND Timestamp = ? AND duration = ? AND heartbeat = ? AND sleep_state = ? AND breathe = ? AND is_stop_snore = ? AND snore = ? AND stop_snore_en = ? AND move = ? AND rmssd = ? AND sdnn = ? AND move_small = ? AND body_energy = ? AND gear = ? AND hf = ? AND lf = ? AND snove_avg = ?
        ''', (device_id,) + tuple(record))
        count = cursor.fetchone()[0]
        if count == 0:
            # 如果记录不存在，则插入
            cursor.execute('''
                INSERT INTO records (device_id, Timestamp, duration, heartbeat, sleep_state, breathe, is_stop_snore, snore, stop_snore_en, move, rmssd, sdnn, move_small, body_energy, gear, hf, lf, snove_avg)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (device_id,) + tuple(record))
            print(f"记录插入成功 (device_id: {device_id}, record: {record})")
        else:
            print(f"记录已存在，跳过插入 (device_id: {device_id}, record: {record})")

    print(f"记录插入完成 (device_id: {device_id})")

def cmd_get_record2_handler(src, device_id):
    records_data = src.get('data', [])
    print(f"提取到的记录数据 (device_id: {device_id}):", records_data)
    # 检查 records 是否为空
    if not records_data:
        print(f"没有记录数据可以插入 (device_id: {device_id})")
        return 0

    # 连接到SQLite数据库（如果数据库不存在，将会创建一个新的）
    conn = sqlite3.connect('python/records.db')
    cursor = conn.cursor()

    # 创建表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS records (
            device_id INTEGER,
            Timestamp TEXT,
            duration INTEGER,
            heartbeat INTEGER,
            sleep_state INTEGER,
            breathe INTEGER,
            is_stop_snore INTEGER,
            snore INTEGER,
            stop_snore_en INTEGER,
            move INTEGER,
            rmssd INTEGER,
            sdnn INTEGER,
            move_small INTEGER,
            body_energy INTEGER,
            gear INTEGER,
            hf INTEGER,
            lf INTEGER,
            snove_avg INTEGER,
            UNIQUE (device_id, Timestamp) ON CONFLICT IGNORE
        )
    ''')
    print("表创建成功")

    # 插入记录
    insert_records(cursor, records_data, device_id)

    # 提交事务
    conn.commit()
    print("事务提交成功")

    # 关闭连接
    conn.close()
    print("数据库连接关闭")

    print(f"数据已成功保存到python/records.db (device_id: {device_id})")
    
    return int(src['count'])

def cmd_get_record_num2_handler(src):
    nums = int(src['nums'])
    day_tp = int(src['day_tp'])
    print("获取到的记录数:", nums)
    return nums, day_tp

def cmd_get_firmware_version_handler(src):
    version = src['version']
    print(f"固件版本: {version} ")
    return version

def cmd_get_network_status_handler(src):
    rssi = src['rssi']
    print(f"wifi rssi: {rssi} ")
    return rssi

def cmd_get_sleep_data_handler(self, src):
    ls_hb  = src['ls_hb']
    ds_hb = src['ds_hb']
    hb = src['hb']
    ls_br = src['ls_br']
    ds_br = src['ds_br']
    br = src['br']
    ls_rmssd = src['ls_rmssd']
    ds_rmssd = src['ds_rmssd']
    rmssd = src['rmssd']
    count = src['count']
    timestamp = src['timestamp']
    self.result_text.insert(tk.END, f"睡眠数据: {ls_hb} {ds_hb} {hb} {ls_br} {ds_br} {br} {ls_rmssd} {ds_rmssd} {rmssd} {count} {timestamp} ")
    return src['rsq']

def cmd_set_sleep_data_handler(self, src):
    print(f"删除睡眠数据: {src['rsq']}")
    return src['rsq']   

def cmd_download_firmware_data_handler(src):
    offset = int(src['offset'])
    size = int(src['size'])
    print(f"固件数据下载成功，偏移:", offset, "大小:", size)
    return offset, size

def calculate_md5(file_path):
    """计算文件的MD5值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

# def response_handler(func):
#     """
#     装饰器，用于处理响应
#     """
#     def wrapper(*args, **kwargs):
#         try:
#             print("发送请求...")
#             response = func(*args, **kwargs)
#             if response.status_code == 200:
#                 print("请求成功")
#                 print_response(response)
#             else:
#                 print(f"请求失败，状态码: {response.status_code}")
#                 print_response(response)
#         except requests.exceptions.RequestException as e:
#             print(f"网络请求异常: {e}")
#         except sqlite3.DatabaseError as e:
#             print(f"数据库操作异常: {e}")
#         except Exception as e:
#             print(f"未知异常: {e}")

#         return response
#     return wrapper

def send_post_request(self, device_id, cmd, day_tp, num_offset=None, url=None, headers=None):
    """
    发送HTTP POST请求

    :param device_id: 设备ID
    :param cmd: 命令
    :param day_tp: 时间戳
    :param num_offset: 偏移量
    :param url: 请求URL
    :param headers: 请求头
    :return: 响应内容
    """
    if cmd == 'get_record2':
        # 定义InputData
        input_data = {
            "cmd": cmd,
            "count": "10",
            "day_tp": str(day_tp),
            "num_offset": str(num_offset),
            "is_upload": "1",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
            
        }
    elif cmd == 'get_record_num2':
        input_data = {
            "cmd": cmd,
            "day_tp": str(day_tp),
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }
    print(f"请求数据 (device_id: {device_id}):", input_data)

    # 将InputData转换为JSON字符串
    input_data_json = json.dumps(input_data)

    # 定义POST请求的数据
    data = {
        "DeviceId": device_id,
        "ServiceName": "Request",
        "InputData": input_data_json,
        "Timeout": 5000
    }
    print(f"POST请求数据 (device_id: {device_id}):", data)

    # 发送POST请求
    response = requests.post(url, json=data, headers=headers)

    # 返回响应内容
    return response

class RecordManager:
    def __init__(self, device_id, start_day_tp=None):
        self.device_id = device_id
        self.day_tp = int(start_day_tp / 86400)
        self.current_date = datetime.now().strftime('%Y-%m-%d')
        self.load_state()
        print(f"初始化 RecordManager (device_id: {self.device_id})")

    def load_state(self):
        if os.path.exists('python/state.json'):
            with open('python/state.json', 'r') as file:
                state = json.load(file)
                device_state = state.get(str(self.device_id))
                if device_state is not None:
                    self.day_tps = device_state.get('day_tps', {})
                    print(f"加载状态 (device_id: {self.device_id}): day_tps={self.day_tps}")
                else:
                    self.day_tps = {}
                    print(f"状态文件不存在 (device_id: {self.device_id})")
        else:
            self.day_tps = {}
            print(f"状态文件不存在 (device_id: {self.device_id})")

    def save_state(self):
        state = {}
        if os.path.exists('python/state.json'):
            with open('python/state.json', 'r') as file:
                state = json.load(file)

        device_state = {
            'day_tps': self.day_tps
        }
        state[str(self.device_id)] = device_state

        with open('python/state.json', 'w') as file:
            json.dump(state, file, indent=4)  # 使用 indent=4 进行格式化
            print(f"保存状态 (device_id: {self.device_id}): day_tps={self.day_tps}")

    def init_num(self, day_tp):
        if str(day_tp) not in self.day_tps:
            self.day_tps[str(day_tp)] = {'nums': 0, 'num_offset': 0}

        response = send_post_request(self,
            device_id=self.device_id,
            cmd="get_record_num2",
            day_tp=int(day_tp * 86400) + 8 * 3600,
            url='http://*************:8089/App/InvokeService',
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        if response.status_code == 200:
            nested_data = response.json()['data']
            if nested_data['data'] != None:
                data = json.loads(nested_data['data'])
                print("提取到的记录:", data)
                if data != None:
                    nums, _ = cmd_get_record_num2_handler(data)
                    if nums != self.day_tps[str(day_tp)]['nums']:
                        self.day_tps[str(day_tp)]['nums'] = nums
                        self.save_state()
                    print(f"初始化 nums 为 {self.day_tps[str(day_tp)]['nums']} (device_id: {self.device_id}, day_tp: {day_tp})")
        else:
            print(f"请求失败，状态码: {response.status_code} (device_id: {self.device_id}, day_tp: {day_tp})")

    def read_records(self, day_tp):
        if str(day_tp) not in self.day_tps:
            self.day_tps[str(day_tp)] = {'nums': 0, 'num_offset': 0}

        while self.day_tps[str(day_tp)]['num_offset'] <= self.day_tps[str(day_tp)]['nums']:
            response = send_post_request(self,
                device_id=self.device_id,
                cmd="get_record2",
                day_tp=int(day_tp * 86400) + 8 * 3600,
                num_offset=self.day_tps[str(day_tp)]['num_offset'],
                url='http://*************:8089/App/InvokeService',
                headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
            )
            if response.status_code == 200:
                print_response(response)
                nested_data = response.json()['data']
                if nested_data == None:
                    break
                # data = json.loads(nested_data['data'])
                data = nested_data['data']
                if data == None:
                    break
                data = json.loads(data)
                record_count = cmd_get_record2_handler(data, self.device_id)
                print(f"提取到{record_count}条记录:", data)
                if record_count == 0:
                    break
            else:
                print(f"请求失败，状态码: {response.status_code} (device_id: {self.device_id}, day_tp: {str(day_tp)})")
                break

            self.day_tps[str(day_tp)]['num_offset'] += record_count
            self.save_state()
            print(f"已读取 {self.day_tps[str(day_tp)]['num_offset']} 条记录，还有 {self.day_tps[str(day_tp)]['nums'] - self.day_tps[str(day_tp)]['num_offset']} 条记录未读 (device_id: {self.device_id}, day_tp: {day_tp})")

    def run_once(self):
        current_day_tp = self.day_tp
        print(f"开始运行 (device_id: {self.device_id}, day_tp: {current_day_tp})")
        while current_day_tp <= (int(time.time()) / 86400):
            # self.load_state()
            self.init_num(current_day_tp)
            self.read_records(current_day_tp)
            print(f"完成一次运行 (device_id: {self.device_id}, day_tp: {current_day_tp})")

            # 更新到下一天
            current_day_tp += 1
            self.day_tp = current_day_tp
            self.save_state()

class MultiDeviceManager:
    def __init__(self, device_ids, start_day_tp=None):
        self.device_managers = {device_id: RecordManager(device_id, start_day_tp) for device_id in device_ids}
        print("初始化 MultiDeviceManager")

    def run_all(self):
        for manager in self.device_managers.values():
            manager.run_once()
        print("所有设备完成一次运行")

    def schedule_all(self):
        for manager in self.device_managers.values():
            schedule.every(1).hours.do(manager.run_once)
        print("所有设备的任务已调度")

class RecordManagerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("记录管理器")

        # 创建输入框和标签
        self.start_label = ttk.Label(root, text="开始日期 (年-月-日):")
        self.start_label.grid(row=0, column=0, padx=10, pady=10)

        self.start_entry = DateEntry(root, date_pattern='yyyy-mm-dd')
        self.start_entry.grid(row=0, column=1, padx=10, pady=10)

        # 创建开始/停止按钮
        self.sync_button = ttk.Button(root, text="开始同步", command=self.toggle_sync)
        self.sync_button.grid(row=0, column=2, padx=10, pady=10)

        # 创建导出按钮
        self.export_button = ttk.Button(root, text="导出Excel", command=self.export_to_excel)
        self.export_button.grid(row=0, column=3, padx=10, pady=10)

        # 创建文本框显示结果
        self.result_text = tk.Text(root, height=10, width=50)
        self.result_text.grid(row=1, column=0, columnspan=4, padx=10, pady=10)

        # 添加设备ID输入框和按钮
        self.add_device_label = ttk.Label(root, text="添加设备ID:")
        self.add_device_label.grid(row=2, column=0, padx=10, pady=10)

        self.add_device_entry = ttk.Entry(root)
        self.add_device_entry.grid(row=2, column=1, padx=10, pady=10)

        self.add_device_button = ttk.Button(root, text="添加", command=self.add_device)
        self.add_device_button.grid(row=2, column=2, padx=10, pady=10)

        # 删除设备ID列表框和按钮
        self.device_listbox = tk.Listbox(root, selectmode=tk.SINGLE)
        self.device_listbox.grid(row=3, column=0, columnspan=2, padx=10, pady=10)

        self.remove_device_button = ttk.Button(root, text="删除", command=self.remove_device)
        self.remove_device_button.grid(row=3, column=2, padx=10, pady=10)

        # OTA升级相关控件
        self.bin_file_label = ttk.Label(root, text="选择BIN文件:")
        self.bin_file_label.grid(row=4, column=0, padx=10, pady=10)

        self.bin_file_entry = ttk.Entry(root, width=30)
        self.bin_file_entry.grid(row=4, column=1, padx=10, pady=10)

        self.bin_file_button = ttk.Button(root, text="浏览", command=self.select_bin_file)
        self.bin_file_button.grid(row=4, column=2, padx=10, pady=10)

        self.device_id_label = ttk.Label(root, text="选择设备ID:")
        self.device_id_label.grid(row=5, column=0, padx=10, pady=10)

        self.device_id_combobox = ttk.Combobox(root)
        self.device_id_combobox.grid(row=5, column=1, padx=10, pady=10)

        self.ota_button = ttk.Button(root, text="OTA升级", command=self.perform_ota_upgrade)
        self.ota_button.grid(row=5, column=2, padx=10, pady=10)

         # 添加读取固件版本按钮
        self.firmware_version_button = ttk.Button(root, text="读取固件版本", command=self.read_firmware_version)
        self.firmware_version_button.grid(row=6, column=0,  padx=10, pady=10)

        self.read_sleep_data_button = ttk.Button(root, text="读取睡眠数据", command=self.read_sleep_data)
        self.read_sleep_data_button.grid(row=6, column=1,  padx=10, pady=10)

        self.format_sleep_data_button = ttk.Button(root, text="删除睡眠数据", command=self.format_sleep_data)
        self.format_sleep_data_button.grid(row=6, column=2, padx=10, pady=10)

        self.dnheartbeat_data_button = ttk.Button(root, text="设置心跳包", command=self.dnheartbeat_data)
        self.dnheartbeat_data_button.grid(row=6, column=3,  padx=10, pady=10)

        self.device_reset_button = ttk.Button(root, text="复位", command=self.device_reset)
        self.device_reset_button.grid(row=7, column=0,  padx=10, pady=10)

        self.read_wifi_rssi_button = ttk.Button(root, text="读取信号强度", command=self.read_wifi_rssi)
        self.read_wifi_rssi_button.grid(row=7, column=1,  padx=10, pady=10)

        self.set_client_id_button = ttk.Button(root, text="设置设备类型", command=self.set_mqtt)
        self.set_client_id_button.grid(row=7, column=2, padx=10, pady=10)

        self.device_client_id = ttk.Label(root, text="设备类型：:")
        self.device_client_id.grid(row=8, column=0, padx=10, pady=10)

        self.device_client_id_combobox = ttk.Combobox(root)
        self.device_client_id_combobox.grid(row=8, column=1, padx=10, pady=10)

        # 添加Excel转JSON相关控件
        self.excel_frame = ttk.LabelFrame(root, text="Excel转JSON")
        self.excel_frame.grid(row=9, column=0, columnspan=4, padx=10, pady=10, sticky="nsew")
        
        self.excel_file_label = ttk.Label(self.excel_frame, text="选择Excel文件:")
        self.excel_file_label.grid(row=0, column=0, padx=10, pady=10)
        
        self.excel_file_entry = ttk.Entry(self.excel_frame, width=30)
        self.excel_file_entry.grid(row=0, column=1, padx=10, pady=10)
        
        self.excel_file_button = ttk.Button(self.excel_frame, text="浏览", command=self.select_excel_file)
        self.excel_file_button.grid(row=0, column=2, padx=10, pady=10)
        
        self.convert_button = ttk.Button(self.excel_frame, text="转换", command=self.convert_excel_to_json)
        self.convert_button.grid(row=0, column=3, padx=10, pady=10)
        
        # 添加结果显示文本框
        self.json_result_text = tk.Text(self.excel_frame, height=10, width=50)
        self.json_result_text.grid(row=1, column=0, columnspan=4, padx=10, pady=10)

        # 初始化同步状态
        self.is_syncing = False
        self.sync_thread = None

        # 加载设备列表
        self.device_ids = self.load_device_ids()
        self.update_device_listbox()
        self.device_id_combobox['values'] = self.device_ids

        self.device_client_id_combobox['values'] = ['测试', '山东', '美纳']

    def load_device_ids(self):
        device_ids = []
        if os.path.exists('python/device_ids.json'):
            with open('python/device_ids.json', 'r') as file:
                device_ids = json.load(file)
        return device_ids

    def save_device_ids(self):
        with open('python/device_ids.json', 'w') as file:
            json.dump(self.device_ids, file, indent=4)

    def add_device(self):
        device_id = self.add_device_entry.get()
        if device_id:
            #device_id = str(device_id)
            if device_id not in self.device_ids:
                self.device_ids.append(device_id)
                self.save_device_ids()
                self.update_device_listbox()
                self.device_id_combobox['values'] = self.device_ids
                self.result_text.insert(tk.END, f"设备ID {device_id} 已添加。\n")
            else:
                self.result_text.insert(tk.END, f"设备ID {device_id} 已存在。\n")
        else:
            self.result_text.insert(tk.END, "请输入有效的设备ID。\n")
        self.add_device_entry.delete(0, tk.END)

    def remove_device(self):
        selected_index = self.device_listbox.curselection()
        if selected_index:
            device_id = self.device_ids[selected_index[0]]
            self.device_ids.remove(device_id)
            self.save_device_ids()
            self.update_device_listbox()
            self.device_id_combobox['values'] = self.device_ids
            self.result_text.insert(tk.END, f"设备ID {device_id} 已删除。\n")

    def update_device_listbox(self):
        self.device_listbox.delete(0, tk.END)
        for device_id in self.device_ids:
            self.device_listbox.insert(tk.END, device_id)

    def toggle_sync(self):
        if self.is_syncing:
            self.stop_sync()
        else:
            self.start_sync()

    def start_sync(self):
        start_date = self.start_entry.get_date()
        # 将 datetime.date 转换为 datetime.datetime
        start_datetime = datetime.combine(start_date, datetime.min.time())
        self.start_day_tp = int(start_datetime.timestamp())
        self.result_text.insert(tk.END, f"开始从 {start_date.strftime('%Y-%m-%d')} 读取记录到今天的。\n")
        # 启动同步线程
        self.is_syncing = True
        self.sync_button.config(text="停止同步")
        self.sync_thread = threading.Thread(target=self.run_schedule)
        self.sync_thread.start()

    def stop_sync(self):
        if self.is_syncing:
            self.is_syncing = False
            self.sync_button.config(text="开始同步")
            schedule.clear()
            if self.sync_thread and self.sync_thread.is_alive():
                self.sync_thread.join()  # 等待线程结束
            self.result_text.insert(tk.END, "同步已停止。\n")

    def run_schedule(self):
        if self.is_syncing:
            self.manager = MultiDeviceManager(self.device_ids, self.start_day_tp)
            self.manager.run_all()
            self.manager.schedule_all()
        while self.is_syncing:
            schedule.run_pending()
            time.sleep(1)

    def export_to_excel(self):
        # 连接到数据库
        conn = sqlite3.connect('python/records.db')
        cursor = conn.cursor()

        # 读取数据
        cursor.execute('SELECT * FROM records')
        rows = cursor.fetchall()
        columns = [description[0] for description in cursor.description]

        # 将数据转换为 DataFrame
        df = pd.DataFrame(rows, columns=columns)

        # 生成 Excel 文件
        excel_file = 'python/records.xlsx'
        df.to_excel(excel_file, index=False)

        # 关闭数据库连接
        conn.close()

        self.result_text.insert(tk.END, f"数据已导出到 {excel_file}\n")

    def select_bin_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("BIN files", "*.bin")])
        if file_path:
            self.bin_file_entry.delete(0, tk.END)
            self.bin_file_entry.insert(0, file_path)
            self.result_text.insert(tk.END, f"选择的BIN文件: {file_path}\n")

    def post_bin_file(self, device_id, file_path):
        try:
            file_name, _ = os.path.splitext(os.path.basename(file_path))  # 去掉后缀名
            offset = 0
            file_size = os.path.getsize(file_path)
            final = 0
            per_size = 512
            read_size = 0
            with open(file_path, 'rb') as file:
                while offset < file_size:
                    file.seek(offset)
                    chunk = file.read(per_size)
                    if offset + per_size >= file_size:
                        final = 1
                    else:
                        final = 0
                    read_size = len(chunk)
                    base64_chunk = base64.b64encode(chunk).decode('utf-8')
                    input_data = {
                        "cmd": "download_firmware_data",
                        "size": str(read_size),
                        "offset": str(offset),
                        "final": str(final),
                        "data": str(base64_chunk),
                        "snr": str(device_id),
                        "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
                    }
            
                    input_data_json = json.dumps(input_data)
                    # 定义读取固件版本请求的数据
                    data = {
                        "DeviceId": device_id,
                        "ServiceName": "Request",
                        "InputData": input_data_json,
                        "Timeout": 20000
                    }
                    print(f"POST请求数据 (device_id: {device_id}):", data)
                    # 发送POST请求
                    response = requests.post(
                        url='http://*************:8089/App/InvokeService',
                        json=data,
                        headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
                    )

                    print_response(response)

                    if response.status_code == 200:
                        try:
                            nested_data = response.json()['data']
                            data = json.loads(nested_data['data'])
                            if data:
                                recv_offset, recv_size = cmd_download_firmware_data_handler(data)
                                if recv_offset == offset:
                                    offset += read_size
                                if offset == file_size:
                                    self.result_text.insert(tk.END, f"设备ID {device_id} OTA升级成功。\n")
                            else:
                                self.result_text.insert(tk.END, f"无法下载设备ID {device_id} 的固件数据。\n")
                        except Exception as e:
                            self.result_text.insert(tk.END, f"解析固件版本响应时发生错误: {e}\n")

                            

                    else:
                        self.result_text.insert(tk.END, f"设备ID {device_id} OTA升级失败，状态码: {response.status_code}\n")
                        break
        except Exception as e:
            self.result_text.insert(tk.END, f"OTA升级过程中发生错误: {e}\n")

    def perform_ota_upgrade(self):
        bin_file_path = self.bin_file_entry.get()
        device_id = self.device_id_combobox.get()

        if not bin_file_path or not device_id:
            self.result_text.insert(tk.END, "请选择BIN文件和设备ID。\n")
            return

        if not os.path.exists(bin_file_path):
            self.result_text.insert(tk.END, f"BIN文件 {bin_file_path} 不存在。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)
        file_name, _ = os.path.splitext(os.path.basename(bin_file_path))  # 去掉后缀名

        # 执行OTA升级操作
        self.result_text.insert(tk.END, f"开始对设备ID {device_id} 进行OTA升级...\n")

        md5_str = calculate_md5(bin_file_path)

        input_data = {
            "cmd": "set_firmware_info",
            "version": file_name,
            "md5sum": md5_str,
            "size": str(os.path.getsize(bin_file_path)),
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }
        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )

        if response.status_code == 200:
            print_response(response)
            time.sleep(2)
            self.post_bin_file(device_id, bin_file_path)
    def read_firmware_version(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        seq = int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp())

        input_data = {
            "cmd": "get_firmware_version",
            "snr": str(device_id),
            "seq": str(seq)
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        # response = requests.post(
        #     url='http://*************:8089/App/InvokeService',
        #     json=data,
        #     headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        # )
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    firmware_version = cmd_get_firmware_version_handler(data)
                    self.result_text.insert(tk.END, f"设备ID {device_id} 的固件版本: {firmware_version}\n")
                else:
                    self.result_text.insert(tk.END, f"无法获取设备ID {device_id} 的固件版本。\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"解析固件版本响应时发生错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"读取设备ID {device_id} 的固件版本失败，状态码: {response.status_code}\n")
            print_response(response)

    def read_sleep_data(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        input_data = {
            "cmd": "get_sleep_data",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    cmd_get_sleep_data_handler(self, data)
            except Exception as e:
                self.result_text.insert(tk.END, f"读取睡眠数据错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"读取设备ID {device_id} 的睡眠数据失败，状态码: {response.status_code}\n")
            print_response(response)
    
    def format_sleep_data(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        input_data = {
            "cmd": "format_sleep_data",
            "format": "1",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 20000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    cmd_set_sleep_data_handler(data)
                    self.result_text.insert(tk.END, f"删除睡眠数据: {data}\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"删除睡眠数据错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"删除设备ID {device_id} 的睡眠数据失败，状态码: {response.status_code}\n")
            print_response(response)

    def dnheartbeat_data(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        # input_data = {
        #     "cmd": "dnHeartbeat",
        #     "code": "0",
        #     "realDataEn": "1",
        #     "realDataDelay":"1",
        #     "realDataMode":"mqtt",
        #     "realDataHost":None,
        #     "realDataPort":None,
        #     "realDataUpload":None,
        #     "tp":str(int(datetime.now().timestamp()))
        # }
        input_data = {
            "cmd" : "set_raw_upload",
            "code": "0",
            "enable": "1",
            "host": "***************",
            "port": "10240",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }
        input_data_json = json.dumps(input_data)
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 1000
        }
        start_ts = int(datetime.now().timestamp() * 1000)
        print(f"POST请求数据 ({start_ts}) (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            print(f"POST请求收到回复 ({int(datetime.now().timestamp() * 1000 - start_ts)}) (rsp code: {response.status_code})")
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    cmd_set_sleep_data_handler(data)
                    self.result_text.insert(tk.END, f"删除睡眠数据: {data}\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"删除睡眠数据错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"删除设备ID {device_id} 的睡眠数据失败，状态码: {response.status_code}\n")
            print_response(response)

    def read_wifi_rssi(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        input_data = {
            "cmd": "get_network_status",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    rssi = cmd_get_network_status_handler(data)
                    self.result_text.insert(tk.END, f"设备ID {device_id} 的RSSI: {rssi}\n")
                else:
                    self.result_text.insert(tk.END, f"无法获取设备ID {device_id} 的RSSI。\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"解析RSSI响应时发生错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"读取设备ID {device_id} 的RSSI失败，状态码: {response.status_code}\n")
            print_response(response)

    def set_mqtt(self):
        device_id = self.device_id_combobox.get()
        device_type = self.device_client_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备类型。\n")
            return
        
        device_id = str(device_id)

        if device_type == '测试':
            product_key = 'pillowtest'
            product_secret = 'pillowtest'
        elif device_type == '山东':
            product_key = 'shangDongPillow'
            product_secret = 'shangDongPillow'
        elif device_type == '美纳':
            product_key = 'meinapillow'
            product_secret = 'meinapillow'

        input_data = {
            "cmd": "set_mqtt",
            "product_key": product_key,
            "product_secret": product_secret,
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    rssi = cmd_get_network_status_handler(data)
                    self.result_text.insert(tk.END, f"设备ID {device_id} 的RSSI: {rssi}\n")
                else:
                    self.result_text.insert(tk.END, f"无法获取设备ID {device_id} 的RSSI。\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"解析RSSI响应时发生错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"读取设备ID {device_id} 的RSSI失败，状态码: {response.status_code}\n")
            print_response(response)

    def device_reset(self):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        input_data = {
            "cmd": "restart",
            "restart": "1",
            "snr": str(device_id),
            "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
        }

        
        input_data_json = json.dumps(input_data)
        # 定义读取固件版本请求的数据
        data = {
            "DeviceId": device_id,
            "ServiceName": "Request",
            "InputData": input_data_json,
            "Timeout": 5000
        }

        print(f"POST请求数据 (device_id: {device_id}):", data)

        # 发送POST请求
        response = requests.post(
            url='http://*************:8089/App/InvokeService',
            json=data,
            headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
        )
        print_response(response)
        if response.status_code == 200:
            try:
                nested_data = response.json()['data']
                data = json.loads(nested_data['data'])
                if data:
                    self.result_text.insert(tk.END, f"设备ID {device_id} 复位成功\n")
                else:
                    self.result_text.insert(tk.END, f"无法获取设备ID {device_id} 的复位。\n")
            except Exception as e:
                self.result_text.insert(tk.END, f"解析复位响应时发生错误: {e}\n")
        else:
            self.result_text.insert(tk.END, f"读取设备ID {device_id} 的复位失败，状态码: {response.status_code}\n")
            print_response(response)

    def select_excel_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xlsx;*.xls")])
        if file_path:
            self.excel_file_entry.delete(0, tk.END)
            self.excel_file_entry.insert(0, file_path)
            self.result_text.insert(tk.END, f"选择的Excel文件: {file_path}\n")

    def convert_excel_to_json(self):
        excel_file = self.excel_file_entry.get()
        if not excel_file:
            self.result_text.insert(tk.END, "请选择Excel文件。\n")
            return
            
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            df = df.convert_dtypes(convert_integer = True)
            
            # 检查必要的列是否存在
            required_columns = ['Timestamp', 'duration', 'heartbeat', 'sleep_state', 'breathe', 
                            'is_stop_snore', 'snore', 'stop_snore_en', 'move', 'rmssd', 
                            'sdnn', 'hf', 'lf', 'gear']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.result_text.insert(tk.END, f"Excel文件缺少以下列: {', '.join(missing_columns)}\n")
                return
                
            # 转换数据
            json_array = []
            for _, row in df.iterrows():
                # 将每个值转换为字符串
                record = [str(row[col]) for col in required_columns]
                json_array.append(record)
                
            # 转换为JSON字符串
            json_str = json.dumps(json_array, ensure_ascii=False, indent=2)
            
            # 显示结果
            self.json_result_text.delete('1.0', tk.END)
            self.json_result_text.insert(tk.END, json_str)
            self.result_text.insert(tk.END, "Excel数据已成功转换为JSON格式。\n")

            self.send_sleep_data(json_array)

            # 添加发送数据的按钮
            # self.send_data_button = ttk.Button(self.excel_frame, text="发送数据", command=lambda: self.send_sleep_data(json_array))
            # self.send_data_button.grid(row=2, column=0, columnspan=4, padx=10, pady=10)
            
        except Exception as e:
            self.result_text.insert(tk.END, f"转换过程中发生错误: {str(e)}\n")

    def send_sleep_data(self, json_array):
        device_id = self.device_id_combobox.get()

        if not device_id:
            self.result_text.insert(tk.END, "请选择设备ID。\n")
            return

        if not device_id:
            self.result_text.insert(tk.END, "设备ID无效。\n")
            return

        device_id = str(device_id)

        total_records = len(json_array)
        success_count = 0

        # 每批发送的记录数
        batch_size = 20
        
        for i in range(0, total_records, batch_size):
            batch_records = json_array[i:i+batch_size]
            batch_data = []
            for record in batch_records:
                batch_data.append(",".join(record))
                
            input_data = {
                "cmd": "set_sleep_data",
                "append": "1",
                # "count": str(len(batch_records)),
                "data": batch_data,
                "snr": str(device_id),
                "seq": str(int(datetime.combine(self.start_entry.get_date(), datetime.min.time()).timestamp()))
            }
            
            input_data_json = json.dumps(input_data)
            data = {
                "DeviceId": device_id,
                "ServiceName": "Request", 
                "InputData": input_data_json,
                "Timeout": 5000
            }

            print(f"POST请求数据 (device_id: {device_id}, records {i+1}-{min(i+batch_size, total_records)}/{total_records}):", data)

            try:
                response = requests.post(
                    url='http://*************:8089/App/InvokeService',
                    json=data,
                    headers={'Content-Type': 'application/json', 'Authorization': 'iot2nnXjm9biwX6y'}
                )
                print_response(response)
                
                if response.status_code == 200:
                    nested_data = response.json()['data']
                    data = json.loads(nested_data['data'])
                    if data:
                        cmd_set_sleep_data_handler(data)
                        success_count += len(batch_records)
                        self.result_text.insert(tk.END, f"成功发送第 {i+1}-{min(i+batch_size, total_records)}/{total_records} 条数据\n")
                    else:
                        self.result_text.insert(tk.END, f"发送第 {i+1}-{min(i+batch_size, total_records)}/{total_records} 条数据失败: 返回数据为空\n")
                else:
                    self.result_text.insert(tk.END, f"发送第 {i+1}-{min(i+batch_size, total_records)}/{total_records} 条数据失败，状态码: {response.status_code}\n")
            
            except Exception as e:
                self.result_text.insert(tk.END, f"发送第 {i+1}-{min(i+batch_size, total_records)}/{total_records} 条数据时发生错误: {str(e)}\n")

            # 添加延时避免请求过快
            # time.sleep(0.5)

        self.result_text.insert(tk.END, f"\n数据发送完成! 成功: {success_count}/{total_records}\n")

if __name__ == "__main__":
    root = tk.Tk()
    app = RecordManagerGUI(root)
    root.mainloop()