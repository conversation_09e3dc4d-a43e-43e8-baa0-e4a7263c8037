#include "config.h"
#include <flashdb.h>
#include <stdio.h>
#include <string.h>
#include "utils_md5.h"
#include "authorize.h"

typedef struct fdb_default_kv_node s_cfg_node;

typedef enum
{
    TYPE_BYTE = 0,
    TYPE_HALFWORD,
    TYPE_WORD,
    TYPE_STRING,
} c_type;

typedef struct
{
    s_cfg_node node;
    c_type type;
    uint8_t init_ok;
} c_obj;

typedef struct
{
    int time_zone;              // 时区
    int massage_en;             // 是否启用按摩
    int massage_duration;       // 按摩持续时间
    int massage_start;          // 按摩时间开始
    int massage_end;            // 按摩时间结束
    int stop_snore_en;          // 是否打开深睡止鼾
    int stop_snore_lvl;         // 止鼾等级
    int stop_snore_lvl2;        // 止鼾等级
    int heat_en;                // 是否启用加温
    int heat_lvl;               // 温度档位
    char wifi_ssid[33];         // wifi ssid
    char wifi_pwd[33];          // wifi 密码
    char host[16];              // 服务器地址
    int port;                   // 服务器端口
    char mq_user[33];           // mqtt用户名
    char mq_pwd[33];            // mqtt密码
    char product_key[128];      // product_key
    char product_secret[128];   // product_secret
    char ble_auth[128];         // 蓝牙连接授权凭证
    char device_model[33];      // 设备型号
    char device_uid[33];        // 设备uid
    int wifi_en;                // wifi使能
    char device_auth[33];       // 授权码
    char sleep_time[64];        // 睡眠时间段
    char ble_name[33];          // 蓝牙名字
    char device_auth2[33];      // 授权码
    int ble_reset_en;           // 蓝牙模组是否重启
    int ble_reset_dis;          // 蓝牙模组断开重启延迟
    int ble_reset_idle;         // 蓝牙模组空闲重启延迟
    int head_height;            // 头部气囊高度
    int neck_height;            // 颈部气囊高度
} __attribute__((packed, aligned(1))) c_value;

static c_obj cfg_tab[] =
    {
        {{CFG_TIME_ZONE, NULL, 4}, TYPE_WORD, 0},
        {{CFG_MASSAGE_EN, NULL, 4}, TYPE_WORD, 0},
        {{CFG_MASSAGE_DURATION, NULL, 4}, TYPE_WORD, 0},
        {{CFG_MASSAGE_START, NULL, 4}, TYPE_WORD, 0},
        {{CFG_MASSAGE_END, NULL, 4}, TYPE_WORD, 0},
        {{CFG_STOP_SNORE_EN, NULL, 4}, TYPE_WORD, 0},
        {{CFG_STOP_SNORE_LVL, NULL, 4}, TYPE_WORD, 0},
        {{CFG_STOP_SNORE_LVL2, NULL, 4}, TYPE_WORD, 0},
        {{CFG_HEAT_EN, NULL, 4}, TYPE_WORD, 0},
        {{CFG_HEAT_LVL, NULL, 4}, TYPE_WORD, 0},
        {{CFG_WIFI_SSID, NULL, 33}, TYPE_STRING, 0},
        {{CFG_WIFI_PWD, NULL, 33}, TYPE_STRING, 0},
        {{CFG_HOST, NULL, 16}, TYPE_STRING, 0},
        {{CFG_PORT, NULL, 4}, TYPE_WORD, 0},
        {{CFG_MQ_USER, NULL, 33}, TYPE_STRING, 0},
        {{CFG_MQ_PASSWORD, NULL, 33}, TYPE_STRING, 0},
        {{CFG_PRODUCT_KEY, NULL, 128}, TYPE_STRING, 0},
        {{CFG_PRODUCT_SECRET, NULL, 128}, TYPE_STRING, 0},
        {{CFG_BLE_AUTH, NULL, 128}, TYPE_STRING, 0},
        {{CFG_DEVICE_MODEL, NULL, 33}, TYPE_STRING, 0},
        {{CFG_DEVICE_UID, NULL, 33}, TYPE_STRING, 0},
        {{CFG_WIFI_EN, NULL, 4}, TYPE_WORD, 0},
        {{CFG_DEV_AUTH, NULL, AUTHORIZE_KEY_SIZE + 1}, TYPE_STRING, 0},
        {{CFG_SLEEP_TIME, NULL, 64}, TYPE_STRING, 0},
        {{CFG_BLE_NAME, NULL, 33}, TYPE_STRING, 0},
        {{CFG_DEV_AUTH2, NULL, AUTHORIZE_KEY_SIZE + 1}, TYPE_STRING, 0},
        {{CFG_BLE_RESET_EN, NULL, 4}, TYPE_WORD, 0},
        {{CFG_BLE_RESET_DIS, NULL, 4}, TYPE_WORD, 0},
        {{CFG_BLE_RESET_IDLE, NULL, 4}, TYPE_WORD, 0},
        {{CFG_HEAD_HEIGHT, NULL, 4}, TYPE_WORD, 0},
        {{CFG_NECK_HEIGHT, NULL, 4}, TYPE_WORD, 0},
};

static uint8_t init_ok = 0;
static c_value cfg_val = {
    .time_zone = 8,
    .massage_en = 0,
    .massage_duration = 10,
    .massage_start = 1200,
    .massage_end = 0,
    .stop_snore_en = 1,
    .stop_snore_lvl = 1,
    .stop_snore_lvl2 = 7,
    .heat_en = 0,
    .heat_lvl = 0,
    .wifi_ssid = "wifi_ssid",
    .wifi_pwd = "wifi_pwd",
    .host = "***********",
    .port = 60000,
    .mq_user = "mqtt_username",
    .mq_pwd = "mqtt_password",
    .product_key = "Pillow",
    .product_secret = "Pillow123",
    .ble_auth = "",
    .device_model = DEVICE_MODEL,
    .device_uid = "",
    .wifi_en = 0,
    .device_auth = "",
    .sleep_time = "",
    .ble_name = "",
    .device_auth2 = "",
    .ble_reset_en = 0,
    .ble_reset_dis = 0,
    .ble_reset_idle = 0,
    .head_height = 0,
    .neck_height = 0,
};

static void kvdb_lock(fdb_db_t db)
{
    // __disable_irq();
}

static void kvdb_unlock(fdb_db_t db)
{
    // __enable_irq();
}

/* KVDB object */
static struct fdb_kvdb kvdb = {0};

/// @brief 查找参数
/// @param name 参数名字
/// @return 非NULL，查找成功
static c_obj *find_obj(char *name)
{
    for (int i = 0; i < ARRAY_SIZE(cfg_tab); i++)
    {
        if ((memcmp(cfg_tab[i].node.key, name, strlen(name)) == 0) && (strlen(name) == strlen(cfg_tab[i].node.key)))
        {
            return &(cfg_tab[i]);
        }
    }
    return NULL;
}

/**
 * @brief 刷新参数到内存
 * @param name 参数名字
 * @return 0: 成功
 */
static int refresh_value(char *name)
{
    struct fdb_blob blob;
    c_obj *obj = find_obj(name);
    if (obj != NULL)
    {
        if (fdb_kv_get_blob(&kvdb, obj->node.key, fdb_blob_make(&blob, obj->node.value, obj->node.value_len)) == 0)
        {
            printf("not find cfg %s\r\n", obj->node.key);
            return -1;
        }
    }
    else
    {
        printf("%s not find cfg name %s\r\n", __FUNCTION__, name);
        return -1;
    }

    return 0;
}

void table_link_value(uint8_t *v_list)
{
    c_obj *obj = NULL;
    int offset = 0;
    for (int i = 0; i < ARRAY_SIZE(cfg_tab); i++)
    {
        obj = find_obj(cfg_tab[i].node.key);
        obj->node.value = v_list + offset;
        offset += obj->node.value_len;
    }
}

/**
 * @brief 初始化参数
 * @param name 参数名字
 * @return 0 成功
 */
static int make_cfg_obj_default(char *name)
{
    struct fdb_blob blob;
    int ret = 0;
    c_obj *obj = find_obj(name);
    if (obj != NULL)
    {
        ret = fdb_kv_set_blob(&kvdb, name, fdb_blob_make(&blob, obj->node.value, obj->node.value_len));
        if (ret != FDB_NO_ERR)
        {
            printf("%s save %s default failed: %d\r\n", __FUNCTION__, name, ret);
            return -1;
        }
    }
    else
    {
        printf("%s not find %s\r\n", __FUNCTION__, name);
        return -1;
    }
    return 0;
}

/**
 * @brief 读取所有参数
 * @param
 */
static void read_all_app_config(void)
{
    struct fdb_kv_iterator iterator;
    fdb_kv_t cur_kv;

    fdb_kv_iterator_init(&kvdb, &iterator);

    while (fdb_kv_iterate(&kvdb, &iterator))
    {
        cur_kv = &(iterator.curr_kv);
        if (cur_kv->value_len == 0)
        {
            printf("\"%s\"save config data error, length mismatch\r\n", cur_kv->name);
            continue;
        }
        if (refresh_value(cur_kv->name) == 0)
        {
            c_obj *object = find_obj(cur_kv->name);
            if (object != NULL)
            {
                object->init_ok = 1;
                switch (object->type)
                {
                case TYPE_BYTE:
                {
                    printf("read(%d) \"%s\", value:%d\r\n", cur_kv->value_len, cur_kv->name, *((uint8_t *)(object->node.value)));
                }
                break;
                case TYPE_HALFWORD:
                {
                    printf("read(%d) \"%s\", value:%d\r\n", cur_kv->value_len, cur_kv->name, *((uint16_t *)(object->node.value)));
                }
                break;
                case TYPE_WORD:
                {
                    printf("read(%d) \"%s\", value:%d\r\n", cur_kv->value_len, cur_kv->name, *((uint32_t *)(object->node.value)));
                }
                break;
                case TYPE_STRING:
                {
                    printf("read(%d) \"%s\", value:%s\r\n", cur_kv->value_len, cur_kv->name, (char *)(object->node.value));
                }
                break;
                }
            }
        }
    }
    for (int i = 0; i < ARRAY_SIZE(cfg_tab); i++)
    {
        if (cfg_tab[i].init_ok == 0)
        {
            if (make_cfg_obj_default(cfg_tab[i].node.key) == 0)
            {
                cfg_tab[i].init_ok = 1;
                printf("create\"%s\", value len:%d\r\n", cfg_tab[i].node.key, cfg_tab[i].node.value_len);
            }
        }
    }
}

void dev_config_init(void)
{
    int ret = 0;
    table_link_value((uint8_t *)&cfg_val);

    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_LOCK, (void *)kvdb_lock);
    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_UNLOCK, (void *)kvdb_unlock);

    ret = fdb_kvdb_init(&kvdb, "cfg", FDB_KVDB_NAME, NULL, NULL);

    if (ret != FDB_NO_ERR)
    {
        printf("app config init fail\r\n");
        goto EXIT_APP_CFG_INIT;
    }
    read_all_app_config();
    printf("app config init successfully\r\n");
    init_ok = 1;
EXIT_APP_CFG_INIT:
    return;
}

/**
 * @brief 读取参数
 * @param name 参数名字
 * @return 非NULL：成功
 */
void *load_cfg(char *name)
{
    if (init_ok == 0)
        return NULL;
    void *ret = NULL;

    c_obj *obj = find_obj(name);

    if (obj != NULL)
    {
        ret = obj->node.value;
    }
    else
    {
        printf("%s not find param (%s)\r\n", __FUNCTION__, name);
    }
    return ret;
}

/**
 * @brief 保存参数
 * @param name 参数名字
 * @param cfg 需要保存的参数
 * @param size 参数长度
 * @return 0 成功
 */
int save_cfg(char *name, void *cfg, uint32_t size)
{
    if (init_ok == 0)
        return -2;
    struct fdb_blob blob;
    int ret = 0;

    c_obj *obj = find_obj(name);

    if (obj != NULL)
    {
        // 判断长度，不能越界
        if (size <= obj->node.value_len)
        {
            int cmp_ret = 0;
            if (obj->type == TYPE_STRING)
            {
                cmp_ret = strncmp((const char *)cfg, (const char *)obj->node.value, obj->node.value_len);
            }
            else
            {
                cmp_ret = memcmp(cfg, obj->node.value, obj->node.value_len);
            }
            // 判断值是否修改了配置，如果需要保存的值和当前值一样，则不保存，否则进行保存
            if (cmp_ret != 0)
            {
                ret = fdb_kv_set_blob(&kvdb, obj->node.key, fdb_blob_make(&blob, cfg, size));
                if (ret != FDB_NO_ERR)
                {
                    printf("save config failed: %d\r\n", ret);
                    ret = -1;
                    goto EXIT_SAVE_CFG;
                }
                refresh_value(obj->node.key);
                printf("save cfg %s success\r\n", obj->node.key);
            }
            else
            {
                printf("cfg [%s] as same as flash, not need to save\r\n", obj->node.key);
            }
        }
        else
        {
            printf("save cfg %s failed\r\n", obj->node.key);
            ret = -3;
        }
    }
    else
    {
        printf("%s not find param (%s)\r\n", __FUNCTION__, name);
    }
EXIT_SAVE_CFG:
    return ret;
}

static c_value default_cfg_val = {
    .time_zone = 8,
    .massage_en = 0,
    .massage_duration = 10,
    .massage_start = 1200,
    .massage_end = 0,
    .stop_snore_en = 1,
    .stop_snore_lvl = 1,
    .stop_snore_lvl2 = 7,
    .heat_en = 0,
    .heat_lvl = 0,
    .wifi_ssid = "wifi_ssid",
    .wifi_pwd = "wifi_pwd",
    // .host = "***********",
    // .port = 60000,
    // .mq_user = "mqtt_username",
    // .mq_pwd = "mqtt_password",
    .ble_auth = "",
    .device_model = DEVICE_MODEL,
    .device_uid = "",
    .wifi_en = 0,
    .device_auth = "",
    .sleep_time = "",
    .ble_name = "",
    .device_auth2 = "",
    .ble_reset_en = 0,
    .ble_reset_dis = 0,
    .ble_reset_idle = 0,
    .head_height = 0,
    .neck_height = 0,
};

/**
 * @brief 清空kvdb
 * @param
 */
void config_set_default(void)
{
    const struct fal_partition *kvdb_partition = fal_partition_find(FDB_KVDB_NAME);

    if (kvdb_partition != NULL)
    {
        printf("kvdb_partition set default start\r\n");

        // fdb_kvdb_deinit(&kvdb);
        // fal_partition_erase_all(kvdb_partition);
        // for (int i = 0; i < ARRAY_SIZE(cfg_tab); i++)
        // {
        //     cfg_tab[i].init_ok = 0;
        // }
        dev_config_init();

        save_cfg(CFG_MASSAGE_EN, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, massage_en), sizeof(default_cfg_val.massage_en));
        save_cfg(CFG_MASSAGE_DURATION, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, massage_duration), sizeof(default_cfg_val.massage_duration));
        save_cfg(CFG_MASSAGE_START, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, massage_start), sizeof(default_cfg_val.massage_start));
        save_cfg(CFG_MASSAGE_END, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, massage_end), sizeof(default_cfg_val.massage_end));
        save_cfg(CFG_STOP_SNORE_EN, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, stop_snore_en), sizeof(default_cfg_val.stop_snore_en));
        save_cfg(CFG_STOP_SNORE_LVL, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, stop_snore_lvl), sizeof(default_cfg_val.stop_snore_lvl));
        save_cfg(CFG_STOP_SNORE_LVL2, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, stop_snore_lvl2), sizeof(default_cfg_val.stop_snore_lvl2));
        save_cfg(CFG_HEAT_EN, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, heat_en), sizeof(default_cfg_val.heat_en));
        save_cfg(CFG_HEAT_LVL, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, heat_lvl), sizeof(default_cfg_val.heat_lvl));
        save_cfg(CFG_WIFI_SSID, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, wifi_ssid), sizeof(default_cfg_val.wifi_ssid));
        save_cfg(CFG_WIFI_PWD, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, wifi_pwd), sizeof(default_cfg_val.wifi_pwd));
        // save_cfg(CFG_HOST, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, host), sizeof(default_cfg_val.host));
        // save_cfg(CFG_PORT, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, port), sizeof(default_cfg_val.port));
        // save_cfg(CFG_MQ_USER, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, mq_user), sizeof(default_cfg_val.mq_user));
        // save_cfg(CFG_MQ_PASSWORD, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, mq_pwd), sizeof(default_cfg_val.mq_pwd));
        save_cfg(CFG_BLE_AUTH, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, ble_auth), sizeof(default_cfg_val.ble_auth));
        save_cfg(CFG_DEVICE_UID, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, device_uid), sizeof(default_cfg_val.device_uid));
        save_cfg(CFG_SLEEP_TIME, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, sleep_time), sizeof(default_cfg_val.sleep_time));
        save_cfg(CFG_WIFI_EN, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, wifi_en), sizeof(default_cfg_val.wifi_en));
        save_cfg(CFG_HEAD_HEIGHT, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, head_height), sizeof(default_cfg_val.head_height));
        save_cfg(CFG_NECK_HEIGHT, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, neck_height), sizeof(default_cfg_val.neck_height));
        printf("kvdb_partition set default end\r\n");
    }
}

int save(char *key, char *value)
{
    if (load_cfg(key) != NULL)
    {
        c_obj *obj = find_obj(key);
        switch (obj->type)
        {
        case TYPE_WORD:
        {
            uint32_t val = (uint32_t)value;
            // sscanf(value, "%u", &val);
            save_cfg(key, &val, sizeof(val));
        }
        break;
        case TYPE_HALFWORD:
        {
            uint32_t val = (uint32_t)value;
            // sscanf(value, "%u", &val);
            save_cfg(key, &val, 2);
        }
        break;
        case TYPE_STRING:
        {
            save_cfg(key, value, strlen(value) + 1);
        }
        break;
        case TYPE_BYTE:
        {
            uint32_t val = (uint32_t)value;
            // sscanf(value, "%u", &val);
            save_cfg(key, &val, 1);
        }
        break;
        }
    }

    return 0;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), save, save, config save);

int config_format(void)
{
    const struct fal_partition *kvdb_partition = fal_partition_find(FDB_KVDB_NAME);
    fdb_kvdb_deinit(&kvdb);
    fal_partition_erase_all(kvdb_partition);
    for (int i = 0; i < ARRAY_SIZE(cfg_tab); i++)
    {
        cfg_tab[i].init_ok = 0;
    }
    nvic_system_reset();
    dev_config_init();

    return 0;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), config_format, config_format, config format);

/**
 * @brief 清空kvdb
 * @param
 */
void config_save_test(int count)
{
    int val = 0;
    while (count--)
    {
        if (*(int *)load_cfg(CFG_MASSAGE_EN))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_MASSAGE_EN, &val, sizeof(val));
        val = *(int *)load_cfg(CFG_MASSAGE_DURATION) + 1;
        save_cfg(CFG_MASSAGE_DURATION, &val, sizeof(val));
        if (*(int *)load_cfg(CFG_STOP_SNORE_EN))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_STOP_SNORE_EN, &val, sizeof(val));
        if (*(int *)load_cfg(CFG_STOP_SNORE_LVL))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_STOP_SNORE_LVL, &val, sizeof(val));
        if (*(int *)load_cfg(CFG_HEAT_EN))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_HEAT_EN, &val, sizeof(val));
        if (*(int *)load_cfg(CFG_HEAT_LVL))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_HEAT_LVL, &val, sizeof(val));
        if (strcmp((char *)load_cfg(CFG_WIFI_SSID), "  ") == 0)
        {
            save_cfg(CFG_WIFI_SSID, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, wifi_ssid), sizeof(default_cfg_val.wifi_ssid));
        }
        else
        {
            save_cfg(CFG_WIFI_SSID, "  ", strlen("  ") + 1);
        }
        if (strcmp((char *)load_cfg(CFG_WIFI_PWD), "  ") == 0)
        {
            save_cfg(CFG_WIFI_PWD, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, wifi_pwd), sizeof(default_cfg_val.wifi_pwd));
        }
        else
        {
            save_cfg(CFG_WIFI_PWD, "  ", strlen("  ") + 1);
        }
        if (strcmp((char *)load_cfg(CFG_HOST), "  ") == 0)
        {
            save_cfg(CFG_HOST, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, host), sizeof(default_cfg_val.host));
        }
        else
        {
            save_cfg(CFG_HOST, "  ", strlen("  ") + 1);
        }
        if (*(int *)load_cfg(CFG_PORT))
        {
            val = 0;
        }
        else
        {
            val = 1;
        }
        save_cfg(CFG_PORT, &val, sizeof(val));
        if (strcmp((char *)load_cfg(CFG_MQ_USER), "  ") == 0)
        {
            save_cfg(CFG_MQ_USER, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, mq_user), sizeof(default_cfg_val.mq_user));
        }
        else
        {
            save_cfg(CFG_MQ_USER, "  ", strlen("  ") + 1);
        }
        if (strcmp((char *)load_cfg(CFG_MQ_PASSWORD), "  ") == 0)
        {
            save_cfg(CFG_MQ_PASSWORD, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, mq_pwd), sizeof(default_cfg_val.mq_pwd));
        }
        else
        {
            save_cfg(CFG_MQ_PASSWORD, "  ", strlen("  ") + 1);
        }
        if (strcmp((char *)load_cfg(CFG_BLE_AUTH), "  ") == 0)
        {
            save_cfg(CFG_BLE_AUTH, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, ble_auth), sizeof(default_cfg_val.ble_auth));
        }
        else
        {
            save_cfg(CFG_BLE_AUTH, "  ", strlen("  ") + 1);
        }
        if (strcmp((char *)load_cfg(CFG_DEVICE_UID), "  ") == 0)
        {
            save_cfg(CFG_DEVICE_UID, ((uint8_t *)&default_cfg_val) + MEMBER_ADDRESS(c_value, device_uid), sizeof(default_cfg_val.device_uid));
        }
        else
        {
            save_cfg(CFG_DEVICE_UID, "  ", strlen("  ") + 1);
        }
    }
}
SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), save_test, config_save_test, config save test);
