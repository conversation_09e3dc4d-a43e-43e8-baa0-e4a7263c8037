<html>
<head>
<META http-equiv="Content-Type" content="text/html">
<style type="text/css">
		h1, h2, h3, h4, h5, h6 {
			font-family : segoe ui;
			color : black;
			background-color : #EDE7D9;
			padding: 0.3em;
		}

		h1 {
			font-size: 1.2em;
		}		

		h2 {
			font-size: 1.2em;
		}

		body {
			font-family : segoe ui;
		}

		td, th {
			padding: 0.5em;
			text-align : left;
			width: 10em;
		}
		th {
			background-color : #EEEEEE;

		}
		th.column1, td.column1 {
			text-align: left;
			width : auto;
		}
		table {
			width : 100%;
			font-size: 0.9em;
		}

		.DRC_summary_header {
			padding-bottom : 0.1em;
			border : 0px solid black;
			width: 100%;
			align: left;
		}

		.DRC_summary_header_col1,
		.DRC_summary_header_col2, 
		.DRC_summary_header_col3 {
			color : black;
			font-size:100%;
			padding : 0em;
			padding-top : 0.2em;
			padding-bottom 0.2em;
			border : 0px solid black;
			vertical-align: top;
			text-align: left;
		}

		.DRC_summary_header_col1 {
			font-weight: bold;
			width: 8em;
		}

		.DRC_summary_header_col2 {
			width: 0.1em;
		
		}

		.DRC_summary_header_col3 {
			width : auto;
		}

		.header_holder {
		    Width = 100%;
		    border = 0px solid green;
		    padding = 0;
		}


		.front_matter, .front_matter_column1, .front_matter_column2, .front_matter_column3
		{
			left : 0;
			top : 0;
			padding: 0em;
			padding-top : 0.1em;
			border : 0px solid black;
			width : 100%;
			vertical-align: top;
			text-align: left;
		}

		.front_matter_column1 {
			width : 8em;
			font-weight: bold;
		}

		.front_matter_column2 {
			width: 0.1em;
		}

		.front_matter_column3 {
			width : auto;
		}

		.total_column1, .total_column {
			font-weight : bold;
		}
		.total_column1 {
			text-align : left;
		}
		.warning, .error {
			color : red;
			font-weight : bold;
		}
		tr.onmouseout_odd {
			background-color : #white;
		}
		tr.onmouseout_even { 
			background-color : #FAFAFA;
		}
		tr.onmouseover_odd, tr.onmouseover_even { 
			background-color : #EEEEEE;
		} 
		a:link, a:visited, .q a:link,.q a:active,.q {
			color: #21489e; 
		}
		a:link.callback, a:visited.callback { 
			color: #21489e;
		}
		a:link.customize, a:visited.customize {
			color: #C0C0C0;
			position: absolute; 
			right: 10px;
		}	
		p.contents_level1 {
			font-weight : bold;
			font-size : 110%;
			margin : 0.5em;
		}
		p.contents_level2 {
			position : relative;
			left : 20px;
			margin : 0.5em;
		}
	</style><script type="text/javascript">
		function coordToMils(coord) {
			var number = coord / 10000;
			
			if (number != number.toFixed(3))
				number = number.toFixed(3);

			return number + 'mil'
		}

		function coordToMM(coord) {
			var number = 0.0254 * coord / 10000;
			
			if (number != number.toFixed(4))
				number = number.toFixed(4);
			
			return number + 'mm'
		}
	
		function convertCoord(coordNode, units) {
			for (var i = 0; i < coordNode.childNodes.length; i++) {
				coordNode.removeChild(coordNode.childNodes[i]);
			}

			var coord = coordNode.getAttribute('value');
			if (coord != null) {
				if (units == 'mm') {
					textNode = document.createTextNode(coordToMM(coord));
					coordNode.appendChild(textNode);
				} else if (units == 'mil') {
					textNode = document.createTextNode(coordToMils(coord));		
					coordNode.appendChild(textNode);	
				}
			}
		}
	
		function convertUnits(unitNode, units) {
			for (var i = 0; i < unitNode.childNodes.length; i++) {
				unitNode.removeChild(unitNode.childNodes[i]);		
			}
		
			textNode = document.createTextNode(units); 
			unitNode.appendChild(textNode);
		}
	
		function changeUnits(radio_input, units) {
			if (radio_input.checked) {
			
				var elements = document.getElementsByName('coordinate');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertCoord(elements[i], units);
					}
				}
	
				var elements = document.getElementsByName('units');
				if (elements) {
					for (var i = 0; i < elements.length; i++) {
						convertUnits(elements[i], units);
					}
				}
			}
		}
	</script><title>Design Rule Verification Report</title>
</head>
<body onload=""><img ALT="Altium" src="
			file://C:\Users\<USER>\Documents\Altium\AD20\Templates\AD_logo.png
		"><h1>Design Rule Verification Report</h1>
<table class="header_holder">
<td class="column1">
<table class="front_matter">
<tr class="front_matter">
<td class="front_matter_column1">Date:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">2025/6/25</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">11:40:31</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Elapsed Time:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3">00:00:02</td>
</tr>
<tr class="front_matter">
<td class="front_matter_column1">Filename:</td>
<td class="front_matter_column2"></td>
<td class="front_matter_column3"><a href="file:///C:\jmwork\jz-project\SP1000\Hardware\sp1000_main_brd\sp1000_main_brd_V1.1.8\sp1000_main_brd_V1.1.8.PcbDoc" class="file"><acronym title="C:\jmwork\jz-project\SP1000\Hardware\sp1000_main_brd\sp1000_main_brd_V1.1.8\sp1000_main_brd_V1.1.8.PcbDoc">C:\jmwork\jz-project\SP1000\Hardware\sp1000_main_brd\sp1000_main_brd_V1.1.8\sp1000_main_brd_V1.1.8.PcbDoc</acronym></a></td>
</tr>
</table>
</td>
<td class="column2">
<table class="DRC_summary_header">
<tr>
<td class="DRC_summary_header_col1">Warnings:</td>
<td class="DRC_summary_header_col2"></td>
<td class="DRC_summary_header_col3">0</td></tr>
<tr>
<td class="DRC_summary_header_col1">Rule Violations:</td>
<td class="DRC_summary_header_col2"></td>
<td class="DRC_summary_header_col3">0</td></tr>
</table>
</td>
</table><a name="IDAWJQKWC3WNDDFJBLREJOFFVDWFZUA1JZO3MMY4VGMRATTGMU0AH"><h2>Summary</h2></a><table>
<tr>
<th class="column1">Warnings</th>
<th class="column2">Count</th>
</tr>
<tr>
<td style="font-weight : bold; text-align : right" class="column1">Total</td>
<td style="font-weight : bold" class="column2">0</td>
</tr>
</table><br><table>
<tr>
<th class="column1">Rule Violations</th>
<th class="column2">Count</th>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDJZBSXKUV2U3KJZSDNJ2HUGELODIRPM4W5K1R5XJOHWVSJ1OXICNP">Clearance Constraint (Gap=0.152mm) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDQ4QSAAI54UCBLZGW4RDHQLIKVDK4EMAJZWLDNQMQBUAQZRKWI0KP">Clearance Constraint (Gap=0.4mm) (InPolygon),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDIH4ZD0FSNGZ4CL14GHFMOFS0ED0R44R53HIHSQMZEWATEET3J53B">Short-Circuit Constraint (Allowed=No) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDNICH40HQ2DOIKAVLC15RBW0MAPIHXZ03WFHZOMJE11H0CNMJBIBM">Un-Routed Net Constraint ( (All) )</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDLUS5TB0IW4RBFKV4R0NAULOG4PSG00PXVULIKSIMAWSVQ3G2WOZG">Modified Polygon (Allow modified: No), (Allow shelved: No)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDHF5V4LJB4DIBDI3YRHO5EQSYOKAVDQ3NFMLRCOND0002WWOYYNHG">Width Constraint (Min=0.1mm) (Max=5mm) (Preferred=0.2mm) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#ID0C5EOKAM04R4GHJ3XOTTPBQ2HCBFPAWPBE4ZG0EVMFFJU3JUA0CJ">Power Plane Connect Rule(Relief Connect )(Expansion=0.508mm) (Conductor Width=0.254mm) (Air Gap=0.254mm) (Entries=4) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#ID1UGTDSN2FNFPLXETB5P1B5TSQHO1WXUOSC4RHZJZ3Z1J0BJWIELD">Hole Size Constraint (Min=0.025mm) (Max=5mm) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#ID1YG1TFCIGB5UI44Q2LRJSRKVDM4GIUMNNDCVKPN5DRYZFQNOOHBB">Hole To Hole Clearance (Gap=0mm) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDEH2TQV3IIQOAHS4S2KE14D342GLQBK4OLEIS3MGBQFLKFQHWVOUF">Minimum Solder Mask Sliver (Gap=0mm) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDPPG1MTGRCCMGY0HK1OU1STX3FLCYHJXFNL3RGIY0TIKKPFAJXW">Silk To Solder Mask (Clearance=0mm) (IsPad),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDFUSDF4JEDFQRCHZF3TTHNB20SG3PTC2NV4NEOQEHQSJNBKRMAYSC">Silk to Silk (Clearance=0mm) (All),(All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#ID42VUB1F1ZBNUBJAHRXFRKNLTHOJ4T5TJHZ3ZPHNZKMUSYUT1ST5F">Net Antennae (Tolerance=0mm) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#ID3RC5ZXGKRXWHDIPOM13GLQ5WQ030YA1RWALEVNQ2DKC000NVHDF">Matched Lengths(Tolerance=0.508mm) (InDifferentialPairClass('All Differential Pairs'))</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_odd" onmouseover="className = 'onmouseover_odd'" onmouseout="className = 'onmouseout_odd'">
<td class="column1"><a href="#IDK3CMUQH1DX1LPXED1YTMWSA0ZCWFMBIFXCM1MUOVOPAIUESON33F">Matched Lengths(Tolerance=0.2mm) (InNetClass('SPIM_IO[0..3]'))</a></td>
<td class="column2">0</td>
</tr>
<tr class="onmouseout_even" onmouseover="className = 'onmouseover_even'" onmouseout="className = 'onmouseout_even'">
<td class="column1"><a href="#IDOEC1VPQGTNUSNEROKYVXYPOUJBCSAYOFXXOOJCNDCLN0CX32ZPVI">Height Constraint (Min=0mm) (Max=25.4mm) (Prefered=12.7mm) (All)</a></td>
<td class="column2">0</td>
</tr>
<tr>
<td style="font-weight : bold; text-align : right" class="column1">Total</td>
<td style="font-weight : bold" class="column2">0</td>
</tr>
</table><br></body>
</html>
