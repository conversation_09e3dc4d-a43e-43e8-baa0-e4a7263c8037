#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "xqueue.h"
#include "gpio_map.h"
#include "ble_protocol.h"
#include "wifi_protocol.h"
#include "arm_math.h"
#include "cJSON.h"
#include "config.h"
#include "wifi_bt_uart.h"
#include "ai_wb2.h"
#include "beep.h"
#include "at_chat.h"
#include "authorize.h"
#include "sleep_record.h"
#include "real_time.h"
#include "sleep_method.h"
#include "reset_flag.h"

#define AIWB2_URC_BUFFER_SIZE 2048
#define AIWB2_RECV_BUFFER_SIZE 2048
#define AIWB2_SEND_BUFFER_SIZE 1024
#define AIWB2_AT_CMD_BUFFER_SIZE 1024

#define STARTUP_LINE "################################################"

typedef enum
{
    MODE_AT = 0,
    MODE_TRANS,
} ble_mode_t;

typedef enum
{
    BLE_DISCONNECTED = 0,
    BLE_CONNECTED,
} ble_status_t;

typedef enum
{
    WIFI_MODE_INIT = 0,
    WIFI_MODE_AP,
    WIFI_MODE_STA,
} wifi_mode_t;

typedef enum
{
    WIFI_DISCONNECTED = 0,
    WIFI_CONNECTED,
    WIFI_CONNECTING,
} wifi_status_t;

typedef enum
{
    MQTT_DISCONNECTED = 0,
    MQTT_CONNECTED,
    MQTT_CONNECTING,
} mqtt_status_t;

typedef enum
{
    MQTT_IDLE = 0, // MQTT空闲状态
    MQTT_SEND,     // MQTT发送状态
    MQTT_WAIT,     // MQTT等待响应状态
    MQTT_RECV,     // MQTT接收状态
    MQTT_TIMEOUT,  // MQTT超时状态
} mqtt_send_t;

typedef struct
{
    char *cmd;  // AT指令
    char *data; // AT指令数据
} at_sender_t;

typedef struct
{
    uint8_t trans_buf[AIWB2_SEND_BUFFER_SIZE]; // 传输数据缓冲区
    int trans_len;                             // 传输数据长度
} aiwb2_trans_t;

typedef struct
{
    int config_step;             // 配置步骤
    char mac[32];                // 蓝牙MAC地址
    ble_mode_t mode;             // 蓝牙工作模式
    ble_status_t status;         // 蓝牙连接状态
    int auth;                    // 鉴权标志
    uint64_t auth_timeout;       // 鉴权超时时间
    int reset_en;                // 复位使能标志
    int wait_wifi;               // 等待WiFi连接标志
    int wait_wifi_delay;         // 等待WiFi连接延时
    uint64_t disconnect_timeout; // 断开连接超时时间
    uint64_t idle_timeout;       // 空闲超时时间
} __attribute__((packed, aligned(1))) aiwb2_ble_t;

typedef struct
{
    int config_step;     // 配置步骤
    int status;          // MQTT状态
    int rety_delay;      // 重连延迟
    char send_and_wait;  // 发送等待标志
    char sub_topic[128]; // 订阅主题
    char pub_topic[128]; // 发布主题
} __attribute__((packed, aligned(1))) wifi_mqtt_t;

typedef struct
{
    int con_id;    // 连接ID
    char host[32]; // 主机地址
    int port;      // 端口号
} __attribute__((packed, aligned(1))) wifi_tcp_t;

typedef struct
{
    char mode;            // WIFI实时数据模式，是MQTT还是TCP
    uint32_t alarm_delay; // 报警上传延时
    uint32_t leave_delay; // 离床上传延时
    uint32_t delay;       // 上传数据延迟
    uint32_t timer;       // 上传数据定时器
    uint32_t upload;      // 是否上传曲线数据
    wifi_tcp_t socket;    // 如果是TCP模式，相关的配置
} __attribute__((packed, aligned(1))) wifi_real_time_t;

typedef struct
{
    uint8_t enable;
    wifi_tcp_t socket; // 如果是TCP模式，相关的配置
} __attribute__((packed, aligned(1))) wifi_raw_upload_t;

typedef struct
{
    int enable;
    int mode;
    int status;
    int config_step;      // 配置步骤
    int sntp_ok;
    int rssi;
    char mac[32];
    int dhcp;
    char ipv4[32];
    char gateway[32];
    int rety_delay;
    wifi_real_time_t real_time;
    wifi_raw_upload_t raw_upload;
    wifi_mqtt_t mqtt; // mqtt配置
    wifi_tcp_t socket;
} __attribute__((packed, aligned(1))) aiwb2_wifi_t;

typedef struct
{
    int enable;
    int result;
    int ble_scan_result;
    int ble_scan_count;
    char ble_mac[32];
} __attribute__((packed, aligned(1))) aiwb2_production_t;

typedef struct
{
    aiwb2_production_t test; // 是否启动产测
    aiwb2_ble_t ble;         // 蓝牙配置
    aiwb2_wifi_t wifi;       // wifi配置
    char sdk_ver[64];        // sdk版本
    char firmware_ver[64];   // 固件版本
    uint8_t ready;           // 模块是否启动
    at_obj_t at;
    at_adapter_t at_adapter;
    at_cmd_t at_cmd;
    uint8_t err_to_init;
    uint32_t hold_ms;
} aiwb2_dev_t;

static void aiwb2_at_send(char *cmd, char *prefix, at_callbatk_t cb, uint8_t retry, uint16_t timeout);
static void aiwb2_startup_handler(char *recvbuf, int size);
static void ble_mac_handler(char *recvbuf, int size);
static void wifi_mac_handler(char *recvbuf, int size);
static void sdk_ver_handler(char *recvbuf, int size);
static void firmware_ver_handler(char *recvbuf, int size);
static void aiwb2_ready_handler(char *recvbuf, int size);
static void ble_connected_handler(char *recvbuf, int size);
static void ble_disconnected_handler(char *recvbuf, int size);
static void ble_data_handler(char *recvbuf, int size);
static void ble_trans_data_handler(char *recvbuf, int size);
static void ble_hex_data_handler(char *recvbuf, int size);
static void unknow_cmd_handler(char *recvbuf, int size);
static void ble_scan_handler(char *recvbuf, int size);
static void wifi_connected_handler(char *recvbuf, int size);
static void wifi_got_ip_handler(char *recvbuf, int size);
static void wifi_disconnected_handler(char *recvbuf, int size);
static void mqtt_connected_handler(char *recvbuf, int size);
static void mqtt_disconnect_handler(char *recvbuf, int size);
static void mqtt_sub_handler(char *recvbuf, int size);
static int ble_send_data_work(at_env_t *e);
static int ble_send_hex_data_work(at_env_t *e);
static int ble_disconnect_work(at_env_t *e);
static int aiwb2_at_init_work(at_env_t *e);
static int aiwb2_reset_work(at_env_t *e);
static int ble_send_data_work(at_env_t *e);
static void ble_data_json_handler_callback(const uint8_t *data, int size);
static void ble_data_hex_handler_callback(const uint8_t *data, int size);
static void ble_disconnect_callback(at_response_t *r);
static void ble_connect_callback(at_response_t *r);
static void ble_config_callback(at_response_t *r);
static void wifi_data_json_handler_callback(const uint8_t *data, int size);
static void wifi_config_callback(at_response_t *r);
static void wifi_got_ip_callback(at_response_t *r);
static void wifi_got_rssi_callback(at_response_t *r);
static void wifi_mqtt_config_callback(at_response_t *r);
static void wifi_mqtt_subscribe_callback(at_response_t *r);
static void aiwb2_at_error(void);
void aiwb2_init(void);

uint8_t push_wifi_status_handler(uint8_t status);

static uint8_t urc_buf[AIWB2_URC_BUFFER_SIZE];   // 读取数据缓存
static uint8_t recv_buf[AIWB2_RECV_BUFFER_SIZE]; // 读取数据缓存
static uint8_t send_buf[AIWB2_SEND_BUFFER_SIZE]; //
// static uint8_t format_buf[AIWB2_SEND_BUFFER_SIZE];
static int send_bufsz = 0;
static char at_send_buf[AIWB2_AT_CMD_BUFFER_SIZE];
static aiwb2_dev_t aiwb2;
static uint8_t wait_for_startup_line = 0;
// static uint64_t ble_break_set;
// static uint64_t ble_idle_set;
static uint8_t aiwb2_start_upgrade = 0;

static const char hex_head[3] = {0xa5, 0xa5, '\0'};

/*
 * @brief    蓝牙初始化命令表
 */
static const char *ble_init_cmds[] = {
    "ATE0\r\n",
    "AT+BLESERUUID=0000fff000001000800000805f9b34fb\r\n",
    "AT+BLETXUUID=0000fff100001000800000805f9b34fb\r\n",
    "AT+BLERXUUID=0000fff200001000800000805f9b34fb\r\n",
    "AT+BLERFPWR=0\r\n",
    // "AT+BLEADVINTV=80\r\n",
    // "AT+BLECONINTV=16,25,2,600\r\n",
    NULL};

static const char *ble_production_init_cmds[] = {
    "ATE0\r\n",
    "AT+BLESERUUID=0000fff000001000800000805f9b34fb\r\n",
    "AT+BLETXUUID=0000fff100001000800000805f9b34fb\r\n",
    "AT+BLERXUUID=0000fff200001000800000805f9b34fb\r\n",
    // "AT+BLECONINTV=14,16,2,600\r\n",
    NULL};

/*
 * @brief   wifi URC表
 */
static utc_item_t urc_table[] = {
    {STARTUP_LINE, "\r\n", aiwb2_startup_handler},
    {"ble_mac", "\r\n", ble_mac_handler},
    {"wifi_mac", "\r\n", wifi_mac_handler},
    {"sdk_version", "\r\n", sdk_ver_handler},
    {"firmware_version", "\r\n", firmware_ver_handler},
    {"ready", "\r\n", aiwb2_ready_handler},
    {"+EVENT:BLE_CONNECT", "\r\n", ble_connected_handler},
    {"+EVENT:BLE_DISCONNECT", "\r\n", ble_disconnected_handler},
    {"+EVENT:WIFI_CONNECT", "\r\n", wifi_connected_handler},
    {"+EVENT:WIFI_GOT_IP", "\r\n", wifi_got_ip_handler},
    {"+EVENT:WIFI_DISCONNECT", "\r\n", wifi_disconnected_handler},
    {"+EVENT:MQTT_CONNECT", "\r\n", mqtt_connected_handler},
    {"+EVENT:MQTT_DISCONNECT", "\r\n", mqtt_disconnect_handler},
    {"+EVENT:MQTT_SUB,", "", mqtt_sub_handler},
    {"+DATA:", "", ble_data_handler},
    {"{\"cmd\":", "}", ble_trans_data_handler},
    {hex_head, "", ble_hex_data_handler},
    {"unknown cmd", "", unknow_cmd_handler},
    {"Devices Found:", "\r\n", ble_scan_handler}};

/*
 * @brief   打开wifi
 */
void aiwb2_open(void)
{
    gpio_bits_set(AIWB2_EN_GPIO, AIWB2_EN_PIN);
    printf("aiwb2 open\r\n");
}
/*
 * @brief   关闭wifi
 */
void aiwb2_close(void)
{
    gpio_bits_reset(AIWB2_EN_GPIO, AIWB2_EN_PIN);
    printf("aiwb2 close\r\n");
}

/**
 * @brief 检测模块启动handler
 * @param recvbuf 接收到的数据
 * @param size 数据长度
 */
static void aiwb2_startup_handler(char *recvbuf, int size)
{
    // 如果已经接收到了ready，则等待启动信息接收完成
    if (wait_for_startup_line == 1)
    {
        if (aiwb2_start_upgrade == 1)
        {
            aiwb2_start_upgrade = 0;
            beep_play(PLAY_AIWB2_UPGRADE);
        }
        // 启动完成后，进行初始化操作
        at_do_work(&(aiwb2.at), aiwb2_at_init_work, &(aiwb2.at));

        wait_for_startup_line = 0;
    }
    // printf("%s", recvbuf);
}

/**
 * @brief 读取BLE MAC的handler
 * @param recvbuf 接收到的数据
 * @param size 数据长度
 */
static void ble_mac_handler(char *recvbuf, int size)
{
    sscanf(recvbuf, "ble_mac:%s\r\n", aiwb2.ble.mac);
    // printf("%s", recvbuf);
}

/**
 * @brief 读取wifi MAC的handler
 * @param recvbuf 接收到的数据
 * @param size 数据长度
 */
static void wifi_mac_handler(char *recvbuf, int size)
{
    sscanf(recvbuf, "wifi_mac:%s\r\n", aiwb2.wifi.mac);
    // printf("%s", recvbuf);
}

/**
 * @brief 读取模组SDK版本的handler
 * @param recvbuf 接收到的数据
 * @param size 数据长度
 */
static void sdk_ver_handler(char *recvbuf, int size)
{
    sscanf(recvbuf, "sdk_version:%s\r\n", aiwb2.sdk_ver);
    // printf("%s", recvbuf);
}

/**
 * @brief 读取模组固件版本的handler
 * @param recvbuf 接收到的数据
 * @param size 数据长度
 */
static void firmware_ver_handler(char *recvbuf, int size)
{
    sscanf(recvbuf, "firmware_version:%s\r\n", aiwb2.firmware_ver);
    // printf("%s", recvbuf);
}

/*
 * @brief   wifi开机就绪事件
 */
static void aiwb2_ready_handler(char *recvbuf, int size)
{
    printf("aiwb2 ready...\r\n");
    if (aiwb2.ready == 1)
    {
        at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
    }
    else
    {
        aiwb2.ready = 1;
        wait_for_startup_line = 1;
    }
}

/// @brief 蓝牙连接处理
/// @param recvbuf
/// @param size
static void ble_connected_handler(char *recvbuf, int size)
{
    printf("BLE connection detected...\r\n");
    aiwb2.ble.status = BLE_CONNECTED;
    aiwb2.ble.mode = MODE_TRANS;
    aiwb2.ble.auth = 0;
    aiwb2.ble.auth_timeout = SET_SEC_DELAY(30);
    // at_do_work(&(aiwb2.at), exit_trans_work, &(aiwb2.at));
    aiwb2.at.env.printf(&(aiwb2.at), "+++");
    aiwb2.hold_ms = SET_MS_DELAY(100);
    aiwb2.ble.mode = MODE_AT;
    if (aiwb2.test.enable == 0)
    {
        beep_play(PLAY_BLE_CONNECTED);
    }
    else
    {
        // aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        // if (trans_json != NULL)
        // {
        //     snprintf(trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, "{\"cmd\":\"ble_test\",\"req_id\":\"123\"}");
        //     trans_json->trans_len = strlen((char *)trans_json->trans_buf);
        //     at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
        // }
        // snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLESEND=42,\"%s\"\r\n", "{\\\"cmd\\\":\\\"ble_test\\\"\\,\\\"req_id\\\":\\\"123\\\"}");
        // at_send_singlline(&(aiwb2.at), aiwb2_blemtu_callback, "AT+BLEMTU=247\r\n");
    }
    aiwb2.ble.disconnect_timeout = 0;
    aiwb2.ble.idle_timeout = 0;
}

/// @brief 蓝牙断开处理
/// @param recvbuf
/// @param size
static void ble_disconnected_handler(char *recvbuf, int size)
{
    real_time_enable(0);
    aiwb2.ble.auth = 0;
    aiwb2.ble.status = BLE_DISCONNECTED;
    aiwb2.ble.auth_timeout = 0;
#if 0 // 是否启用蓝牙空闲重启和断开重启
    if (aiwb2.ble.reset_en)
    {
        aiwb2.ble.disconnect_timeout = get_tick() + (*((int *)load_cfg(CFG_BLE_RESET_DIS)));
    }
    else
    {
        aiwb2.ble.disconnect_timeout = 0;
    }
    if (aiwb2.ble.reset_en)
    {
        aiwb2.ble.idle_timeout = get_tick() + (*((int *)load_cfg(CFG_BLE_RESET_IDLE))) * 1000;
    }
    else
    {
        aiwb2.ble.idle_timeout = 0;
    }
#endif
    printf("BLE disconnect detected...\r\n");
    // at_send_singlline(&(aiwb2.at), ble_set_mode_close_callback, "AT+BLEMODE=9\r\n");
}

/// @brief 蓝牙接收到数据处理
/// @param recvbuf 数据
/// @param size 数据大小
static void ble_data_handler(char *recvbuf, int size)
{
    uint8_t *p_data = NULL;
    uint32_t data_length = 0;
#if 0
    sscanf(recvbuf, "+DATA:%d,", &data_length);
    for (int i = 0; i < size; i++)
    {
        if (recvbuf[i] == ',')
        {
            p_data = (uint8_t *)recvbuf + i + 1;
            size = size - i - 1;
            break;
        }
    }
#else
    char data_recv[8];
    uint8_t data_recv_count = 0;
    uint32_t read_delay = 0;

    read_delay = SET_SEC_DELAY(1);
    while (IS_TIMEOUT(read_delay) == 0)
    {
        if (aiwb2.at_adapter.read(data_recv + data_recv_count, 1) == 1)
        {
            // read_delay = SET_SEC_DELAY(1);
            char ch = data_recv[data_recv_count];
            data_recv_count++;
            if (ch == ',')
            {
                data_recv[data_recv_count] = '\0';
                break;
            }
        }
    }
    sscanf(data_recv, "%d,", &data_length);

    p_data = calloc(1, data_length + 1);
    alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (unsigned int)p_data);

    if (p_data == NULL)
    {
        printf("ble recv, no memory to read\r\n");
        system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        return;
    }
    read_delay = SET_SEC_DELAY(1);
    data_recv_count = 0;
    while (IS_TIMEOUT(read_delay) == 0)
    {
        if (aiwb2.at_adapter.read(p_data + data_recv_count, 1) == 1)
        {
            // read_delay = SET_SEC_DELAY(1);
            data_recv_count++;
            if (data_recv_count >= data_length)
            {
                // p_data[data_length] = '\0';
                break;
            }
        }
    }
#endif
    // memcpy(recv_buf, p_data, size);
    if (p_data[0] == 0xa5 && p_data[1] == 0xa5)
    {
        printf("%s%s\r\n", recvbuf, data_recv);
        // printf("hex data:");
        // for(int i = 0; i < data_length; i++)
        // {
        //     printf("%02x", p_data[i]);
        // }
        // printf("\r\n");
    }
    else
    {
        printf("%s%s%s\r\n", recvbuf, data_recv, p_data);
    }
    if (ble_json_check(p_data, strlen((char *)p_data) + 1) == 0)
    {
        // 处理JSON并切换到写入状态
        int ret = ble_json_handler(p_data, strlen((char *)p_data) + 1, ble_data_json_handler_callback);
        if (ret == -1)
        {
            // at_send_singlline(&aiwb2.at, ble_disconnect_callback, "AT+BLEDISCON\r\n");
            at_do_work(&aiwb2.at, ble_disconnect_work, NULL);
        }
    }
    else if (ble_hex_check(p_data, data_length) == 0)
    {
        int ret = ble_hex_handler((uint8_t *)p_data, data_length, ble_data_hex_handler_callback);
        if (ret == -1)
        {
            // at_send_singlline(&aiwb2.at, ble_disconnect_callback, "AT+BLEDISCON\r\n");
            at_do_work(&aiwb2.at, ble_disconnect_work, NULL);
        }
    }
#if 1
    if (p_data != NULL)
    {
        free(p_data);
    }
#endif
}

/// @brief 蓝牙json数据处理
/// @param recvbuf 数据
/// @param size 数据大小
static void ble_trans_data_handler(char *recvbuf, int size)
{
#if 0
    if (aiwb2.ble.status == BLE_DISCONNECTED)
    {
        return;
    }
    memcpy(recv_buf, recvbuf, size);

    if (ble_json_check(recv_buf, strlen((char *)recv_buf) + 1) == 0)
    {
        // 处理JSON并切换到写入状态
        int ret = ble_json_handler(recv_buf, strlen((char *)recv_buf) + 1, ble_data_json_handler_callback);
        if (ret == -1)
        {
            // at_send_singlline(&aiwb2.at, ble_disconnect_callback, "AT+BLEDISCON\r\n");
            at_do_work(&aiwb2.at, ble_disconnect_work, NULL);
        }
    }
#endif
}

/// @brief 蓝牙hex数据处理
/// @param recvbuf 数据
/// @param size 数据大小
static void ble_hex_data_handler(char *recvbuf, int size)
{
#if 0
    if (aiwb2.ble.status == BLE_DISCONNECTED)
    {
        return;
    }
    if (ble_hex_check((uint8_t *)recvbuf, size) == 0)
    {
        int ret = ble_hex_handler((uint8_t *)recvbuf, size, ble_data_hex_handler_callback);
        if (ret == -1)
        {
            // at_send_singlline(&aiwb2.at, ble_disconnect_callback, "AT+BLEDISCON\r\n");
            at_do_work(&aiwb2.at, ble_disconnect_work, NULL);
        }
    }
#endif
}

static void unknow_cmd_handler(char *recvbuf, int size)
{
}

/// @brief 蓝牙扫描结果处理
/// @param recvbuf 数据
/// @param size 数据大小
static void ble_scan_handler(char *recvbuf, int size)
{
    char *p_data = NULL;
    uint32_t read_delay = 0;
    uint32_t data_recv_count = 0;
    uint8_t get_device = 0;
    uint8_t get_result = 0;
    int dev_sum = 0;
    int dev_count = 0;

    p_data = (char *)calloc(1, 128);
    alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (unsigned int)p_data);

    if (p_data == NULL)
    {
        printf("ble scan, no memory to read result\r\n");
        system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        return;
    }

    data_recv_count = 0;
    memcpy(p_data, recvbuf, size);
    data_recv_count += size;
    read_delay = SET_SEC_DELAY(1);
    while (IS_TIMEOUT(read_delay) == 0)
    {
        if (aiwb2.at_adapter.read(p_data + data_recv_count, 1) == 1)
        {
            // read_delay = SET_SEC_DELAY(1);
            data_recv_count++;
            if (p_data[data_recv_count - 1] == '\n' && p_data[data_recv_count - 2] == '\r')
            {
                if (strstr(p_data, "rssi"))
                {
                    get_device = 1;
                    break;
                }
            }
        }
    }

    if (get_device == 1)
    {
        char *line_data = strtok(p_data, "\r\n");
        while (line_data != NULL)
        {
            // printf("%s\r\n", line_data);
            if (strncmp(line_data, "Devices Found:", strlen("Devices Found:")) == 0)
            {
                sscanf(line_data, "Devices Found:%d/%d", &dev_count, &dev_sum);
            }
            else if (strncmp(line_data, "name:ZT-TEST_", strlen("name:ZT-TEST_")) == 0)
            {
                get_result = 1;
            }
            else if (strncmp(line_data, "MAC:", strlen("MAC:")) == 0 && get_result == 1)
            {
                memset(aiwb2.test.ble_mac, 0, sizeof(aiwb2.test.ble_mac));
                memcpy(aiwb2.test.ble_mac, line_data + 4, strlen(line_data) - 4);
                aiwb2.test.ble_scan_result = 1;
                printf("find sn zt device, mac: %s\r\n", aiwb2.test.ble_mac);
            }
            line_data = strtok(NULL, "\r\n");
        }
    }
    free(p_data);
    if (dev_count == dev_sum)
    {
        if (aiwb2.test.ble_scan_result == 0)
        {
            if (aiwb2.test.ble_scan_count < 5)
            {
                at_send_singlline(&(aiwb2.at), NULL, "AT+BLESCAN\r\n");
                aiwb2.test.ble_scan_count++;
            }
            else
            {
                aiwb2.test.result = -1;
                printf("aiwb2 can not find ble server\r\n");
            }
        }
        else
        {
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLECONNECT=%s\r\n", aiwb2.test.ble_mac);
            at_send_singlline(&(aiwb2.at), ble_connect_callback, at_send_buf);
        }
    }
}

/*
 * @brief   wifi连接事件
 */
static void wifi_connected_handler(char *recvbuf, int size)
{
    printf("WIFI connection detected...\r\n");
}

/*
 * @brief   wifi断连事件
 */
static void wifi_disconnected_handler(char *recvbuf, int size)
{
    printf("WIFI connection losses...\r\n");
    aiwb2.wifi.status = WIFI_DISCONNECTED;
    if (aiwb2.wifi.real_time.mode == 0)
    {
        real_time_enable(0);
    }
}

/// @brief 处理获取到WIFI IP事件
/// @param recvbuf
/// @param size
static void wifi_got_ip_handler(char *recvbuf, int size)
{
    printf("WIFI got ip...\r\n");
    aiwb2.wifi.status = WIFI_CONNECTED;
    aiwb2_at_send("AT+WJAP?\r\n", "OK\r\n", wifi_got_ip_callback, 3, 3000);
    if (aiwb2.ble.wait_wifi == 1)
    {
        if (strncmp(aiwb2.firmware_ver, "Release-V4.18_P2.12.1-a5107b3", strlen("Release-V4.18_P2.12.1-a5107b3")) == 0)
        {
            aiwb2_at_send("AT+OTA=1,\"aithinker111.oss-cn-beijing.aliyuncs.com\",80,\"/WB2_COMBO_2.13.1.xz\"\r\n", "OK\r\n", NULL, 3, 5000);
            aiwb2_at_send("AT+OTA\r\n", "OK\r\n", NULL, 3, 5000);
            aiwb2_start_upgrade = 1;
        }
#if BAND_PROJECT == 1
        beep_play(PLAY_WIFI_CONNECTED);
#endif
        push_wifi_status_handler(WIFI_STATUS_WJAP_SUCCESS);
        aiwb2.ble.wait_wifi = 2;
        aiwb2.ble.wait_wifi_delay = SET_SEC_DELAY(10);
    }
    aiwb2.wifi.enable = 1;
    save_cfg(CFG_WIFI_EN, (void *)&(aiwb2.wifi.enable), sizeof(aiwb2.wifi.enable));
}

/// @brief 处理MQTT连接成功事件
/// @param recvbuf 数据
/// @param size 数据大小
static void mqtt_connected_handler(char *recvbuf, int size)
{
    printf("MQTT Connected...\r\n");
    aiwb2.wifi.mqtt.status = MQTT_CONNECTED;
    // 订阅subscribe
    snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTTSUB=%s,0\r\n", aiwb2.wifi.mqtt.sub_topic);
    aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_subscribe_callback, 3, 10000);

    if (aiwb2.ble.wait_wifi == 2)
    {
#if BAND_PROJECT == 1
        beep_play(PLAY_MQTT_CONNECTED);
#endif
        push_wifi_status_handler(WIFI_STATUS_MQTT_SUCCESS);
        aiwb2.ble.wait_wifi = 0;
    }
}

/// @brief 处理MQTT连接成功事件
/// @param recvbuf 数据
/// @param size 数据大小
static void mqtt_disconnect_handler(char *recvbuf, int size)
{
    // if (aiwb2.wifi.real_time.mode == 1)
    // {
    //     real_time_enable(0);
    // }
    printf("MQTT Disconnected...\r\n");
    aiwb2.wifi.mqtt.status = MQTT_DISCONNECTED;
    aiwb2.wifi.mqtt.config_step = 0;
    if (aiwb2.wifi.status == WIFI_CONNECTED)
    {
        wifi_mqtt_config_callback(NULL);
    }
    else
    {
        // snprintf(at_send_buf, sizeof(at_send_buf), "AT+WJAP=%s,%s\r\n", (char *)load_cfg(CFG_WIFI_SSID), (char *)load_cfg(CFG_WIFI_PWD));
        // aiwb2_at_send(at_send_buf, "OK\r\n", wifi_wjap_callback, 3, 20000);
    }
}

/**
 * @brief 处理MQTT订阅消息的回调函数
 * 
 * 该函数负责接收和处理MQTT订阅主题收到的消息。它首先解析消息长度，
 * 然后读取完整消息内容，最后对JSON格式的消息进行处理。
 * 
 * @param recvbuf 接收到的初始数据缓冲区
 * @param size 初始数据的大小
 */
static void mqtt_sub_handler(char *recvbuf, int size)
{
    char *p_data = NULL;           // 用于存储完整消息内容的指针
    uint32_t data_length = 0;      // 消息数据长度
    char data_recv[16];            // 临时接收缓冲区，用于解析消息长度
    int data_recv_count = 0;       // 接收数据计数器
    uint32_t read_delay = 0;       // 读取超时计时器
    int comma_count = 0;           // 逗号计数器，用于解析消息格式

    // 设置1秒超时时间
    read_delay = SET_SEC_DELAY(1);
    // 读取AT消息头部，直到找到第二个逗号，这部分包含消息长度信息
    while (IS_TIMEOUT(read_delay) == 0)
    {
        if (aiwb2.at_adapter.read(data_recv + data_recv_count, 1) == 1)
        {
            // read_delay = SET_SEC_DELAY(1);
            char ch = data_recv[data_recv_count];
            if (comma_count >= 1)
            {
                data_recv_count++;
            }
            if (ch == ',')
            {
                comma_count++;
                if (comma_count >= 2)
                {
                    break;
                }
            }
        }
    }
    // 如果未找到两个逗号，说明格式不正确，退出处理
    if (comma_count < 2)
    {
        return;
    }
    // 添加字符串结束符并解析消息长度
    data_recv[data_recv_count] = '\0';
    sscanf(data_recv, "%d,", &data_length);

    // 分配内存存储完整消息内容
    p_data = (char *)calloc(1, data_length + 1);

    alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (unsigned int)p_data);

    // 内存分配失败处理
    if (p_data == NULL)
    {
        printf("mqtt recv, no memory to read\r\n");
        system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        return;
    }
    // 重置超时计时器和计数器，准备读取消息内容
    read_delay = SET_SEC_DELAY(1);
    data_recv_count = 0;
    // 读取完整消息内容
    while (IS_TIMEOUT(read_delay) == 0)
    {
        if (aiwb2.at_adapter.read(p_data + data_recv_count, 1) == 1)
        {
            // read_delay = SET_SEC_DELAY(1);
            data_recv_count++;
            // 当读取的数据达到预期长度时退出循环
            if (data_recv_count >= data_length)
            {
                // p_data[data_length] = '\0';
                break;
            }
        }
    }

    // 打印完整的接收信息
    printf("%s%s%s\r\n", recvbuf, data_recv, p_data);

    // 检查并处理JSON格式的消息
    if (wifi_json_check((uint8_t *)p_data, strlen((char *)p_data) + 1) == 0)
    {
        // 处理JSON并调用回调函数
        int ret = wifi_json_handler(p_data, strlen((char *)p_data) + 1, wifi_data_json_handler_callback);
        if (ret == -1)
        {
            // JSON处理失败的情况，当前未实现具体处理
        }
    }
#if 1
    // 释放分配的内存
    if (p_data != NULL)
    {
        free(p_data);
    }
#endif
}

/// @brief 蓝牙发送JSON数据流程
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int ble_send_data_work(at_env_t *e)
{
    static uint32_t rety_count = 0;
    at_obj_t *a = &(aiwb2.at);
    aiwb2_trans_t *p_trans = (aiwb2_trans_t *)e->params;
    // 获取需要发送的数据
    if (p_trans != NULL)
    {
        send_bufsz = p_trans->trans_len;
        memcpy(send_buf, p_trans->trans_buf, p_trans->trans_len);
        send_buf[send_bufsz] = '\0';
        free(e->params);
        e->params = NULL;
    }
    // 如果蓝牙断开了，则中断发送
    if (aiwb2.ble.status == BLE_DISCONNECTED && e->state == 0)
    {
        return true;
    }
    switch (e->state)
    {
    case 0:
    {
        // 如果蓝牙是透传模式，则发送+++退出透传
        if (aiwb2.ble.mode == MODE_TRANS)
        {
            a->recv_cnt = 0;
            e->printf(a, "+++");
            e->reset_timer(a);
            e->state++;
        }
        else
        {
            e->state = 2;
        }
    }
    break;
    case 1:
    {
        // 等待50ms退出透传模式
        if (e->is_timeout(a, 50))
        {
            if (e->recvlen(a))
            {
                //(e->recvbuf(a))[e->recvlen(a)] = '\0';
                AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            }
            e->state++;
        }
    }
    break;
    case 2:
    {
        // 可以尝试发送3次
        if (rety_count == 0)
        {
            rety_count = 3;
        }
        a->recv_cnt = 0;
        // 发起发送数据的AT指令
        e->printf(a, "AT+BLESENDRAW=%d\r\n", send_bufsz);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 3:
    {
        // 判断是否返回错误
        if (strstr((const char *)(e->recvbuf(a)), "ERROR"))
        {
            if (rety_count > 0)
            {
                rety_count--;
                if (rety_count == 0)
                {
                    e->state++;
                }
                else
                {
                    e->state = 2;
                }
            }
            else
            {
                e->state++;
            }
            e->reset_timer(a);
        }
        // 等待>的返回
        if (e->is_timeout(a, 3000) || strstr((const char *)(e->recvbuf(a)), ">"))
        {
            if (strstr((const char *)(e->recvbuf(a)), ">"))
            {
                AT_DEBUG("<-%s", e->recvbuf(a));
                a->recv_cnt = 0;
                // 发送数据
                for (int i = 0; i < send_bufsz; i++)
                {
                    a->adap.write(send_buf + i, 1);
                    printf("%c", send_buf[i]);
                }
                printf("\r\n");
            }

            e->reset_timer(a);
            e->state++;
        }
    }
    break;
    case 4:
    {
        // 等待返回OK
        if (strstr((const char *)(e->recvbuf(a)), "OK") && (a->recv_cnt >= 2))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            send_bufsz = 0;
            rety_count = 0;
            return true;
        }
        // 如果超时，则重新发送
        if (e->is_timeout(a, 15000))
        {
            if (rety_count > 0)
            {
                rety_count--;
                if (rety_count == 0)
                {
                    printf("ble send error, aiwb2 reset\r\n");
                    at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
                    return true;
                }
                else
                {
                    e->state = 2;
                }
            }
            else
            {
                send_bufsz = 0;
                return true;
            }
            e->reset_timer(a);
        }
        break;
    }
    }
    return false;
}

/// @brief 蓝牙发送HEX数据流程
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int ble_send_hex_data_work(at_env_t *e)
{
    at_obj_t *a = &(aiwb2.at);
    aiwb2_trans_t *p_trans = (aiwb2_trans_t *)e->params;
    // 获取需要发送的数据
    if (p_trans != NULL)
    {
        send_bufsz = p_trans->trans_len;
        memcpy(send_buf, p_trans->trans_buf, p_trans->trans_len);
        free(e->params);
        e->params = NULL;
    }
    // 如果蓝牙已经断开了，则退出发送流程
    if (aiwb2.ble.status == BLE_DISCONNECTED && e->state == 0)
    {
        return true;
    }
    switch (e->state)
    {
    case 0:
    {
        // 如果蓝牙是透传模式，则发送+++退出透传模式
        if (aiwb2.ble.mode == MODE_TRANS)
        {
            a->recv_cnt = 0;
            e->printf(a, "+++");
            e->reset_timer(a);
            e->state++;
        }
        else
        {
            e->state = 2;
        }
    }
    break;
    case 1:
    {
        // 延时等待退出透传模式
        if (e->is_timeout(a, 50))
        {
            if (e->recvlen(a))
            {
                AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            }
            e->state++;
        }
    }
    break;
    case 2:
    {
        // 发起发送数据的AT指令
        a->recv_cnt = 0;
        e->printf(a, "AT+BLESENDRAW=%d\r\n", send_bufsz, send_buf);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 3:
        // 等待返回>
        if (e->is_timeout(a, 3000) || strstr((const char *)(e->recvbuf(a)), ">"))
        {
            AT_DEBUG("<-%s", e->recvbuf(a));
            a->recv_cnt = 0;
            // 收到>后，把数据发送出去
            for (int i = 0; i < send_bufsz; i++)
            {
                a->adap.write(send_buf + i, 1);
                printf("%02x", send_buf[i]);
            }
            printf("\r\n");
            // a->adap.write("\"\r\n", strlen("\"\r\n"));
            e->reset_timer(a);
            e->state++;
        }
        break;
    case 4:
    {
        // 等待发送OK
        if (e->is_timeout(a, 15000) || (strstr((const char *)(e->recvbuf(a)), "OK") && (a->recv_cnt >= 2)))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            send_bufsz = 0;
            aiwb2.ble.mode = MODE_AT;
            return true;
        }
        break;
    }
    }
    return false;
}

/// @brief 蓝牙断开流程
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int ble_disconnect_work(at_env_t *e)
{
    at_obj_t *a = &(aiwb2.at);

    if (aiwb2.ble.status == BLE_DISCONNECTED)
    {
        return true;
    }
    switch (e->state)
    {
    case 0:
    {
        // 如果是透传模式，则需要发送+++退出透传模式
        if (aiwb2.ble.mode == MODE_TRANS)
        {
            a->recv_cnt = 0;
            e->printf(a, "+++");
            e->reset_timer(a);
            e->state++;
        }
        else
        {
            e->state = 2;
        }
    }
    break;
    case 1:
    {
        // 延时50ms，接收完串口剩余数据
        if (e->is_timeout(a, 50))
        {
            if (e->recvlen(a))
            {
                AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            }
            e->state++;
        }
    }
    break;
    case 2:
    {
        // 清空串口接收缓存
        a->recv_cnt = 0;
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 3:
        // 延时1S，断开蓝牙连接
        if (e->is_timeout(a, 1000))
        {
            e->printf(a, "AT+BLEDISCON\r\n");
            e->reset_timer(a);
            e->state++;
        }
        break;
    case 4:
    {
        // 等待返回OK
        if ((strstr((const char *)(e->recvbuf(a)), "OK") && (a->recv_cnt >= 2)))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            ble_disconnect_callback(NULL);
            aiwb2.ble.mode = MODE_AT;
            return true;
        }
        break;
    }
    }
    return false;
}

/// @brief 蓝牙发送JSON数据流程
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int mqtt_send_data_work(at_env_t *e)
{
    static uint32_t rety_count = 0;
    at_obj_t *a = &(aiwb2.at);
    aiwb2_trans_t *p_trans = (aiwb2_trans_t *)e->params;
    // 获取需要发送的数据
    if (p_trans != NULL)
    {
        send_bufsz = p_trans->trans_len;
        memcpy(send_buf, p_trans->trans_buf, p_trans->trans_len);
        send_buf[send_bufsz] = '\0';
        free(e->params);
        e->params = NULL;
    }
    // 如果蓝牙断开了，则中断发送
    //  if (aiwb2.mqtt.config_step == BLE_DISCONNECTED && e->state == 0)
    //  {
    //      return true;
    //  }
    if (aiwb2.wifi.mqtt.status != MQTT_CONNECTED)
    {
        send_bufsz = 0;
        return true;
    }
    switch (e->state)
    {
    case 0:
    {
        // 可以尝试发送3次
        if (rety_count == 0)
        {
            rety_count = 3;
        }
        a->recv_cnt = 0;

        // 发起发送数据的AT指令
        e->printf(a, "AT+MQTTPUBRAW=%s,1,0,%d\r\n", aiwb2.wifi.mqtt.pub_topic, send_bufsz);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 1:
    {
        // 判断是否返回错误
        if (strstr((const char *)(e->recvbuf(a)), "ERROR"))
        {
            if (rety_count > 0)
            {
                rety_count--;
                if (rety_count == 0)
                {
                    e->state++;
                }
                else
                {
                    e->state = 2;
                }
            }
            else
            {
                e->state++;
            }
            e->reset_timer(a);
        }
        // 等待>的返回
        if (e->is_timeout(a, 3000) || strstr((const char *)(e->recvbuf(a)), ">"))
        {
            if (strstr((const char *)(e->recvbuf(a)), ">"))
            {
                AT_DEBUG("<-%s", e->recvbuf(a));
                a->recv_cnt = 0;
                // 发送数据
                for (int i = 0; i < send_bufsz; i++)
                {
                    // if(send_buf[i] == '\"')
                    // {
                    //     a->adap.write("\\", 1);
                    //     printf("\\");
                    // }
                    a->adap.write(send_buf + i, 1);
                    printf("%c", send_buf[i]);
                }
                a->adap.write("\r\n", 2);
                printf("\r\n");
            }

            e->reset_timer(a);
            e->state++;
        }
    }
    break;
    case 2:
    {
        // 等待返回OK
        if (strstr((const char *)(e->recvbuf(a)), "OK") && (a->recv_cnt >= 2))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            rety_count = 0;
            send_bufsz = 0;
            return true;
        }
        // 如果超时，则重新发送
        if (e->is_timeout(a, 15000))
        {
            if (rety_count > 0)
            {
                rety_count--;
                if (rety_count == 0)
                {
                    printf("mqtt send error, aiwb2 reset\r\n");
                    at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
                    return true;
                }
                else
                {
                    e->state = 0;
                }
            }
            else
            {
                send_bufsz = 0;
                return true;
            }
            e->reset_timer(a);
        }
        break;
    }
    }
    return false;
}

/// @brief tcp连接发送数据状态机
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int tcp_send_data_work(at_env_t *e)
{
    at_obj_t *a = &(aiwb2.at);
    aiwb2_trans_t *p_trans = (aiwb2_trans_t *)e->params;
    // 获取需要发送的数据
    if (p_trans != NULL)
    {
        send_bufsz = p_trans->trans_len;
        memcpy(send_buf, p_trans->trans_buf, p_trans->trans_len);
        free(e->params);
        e->params = NULL;
    }
    if (strlen(aiwb2.wifi.socket.host) == 0 || aiwb2.wifi.socket.port == 0)
    {
        return true;
    }
    if (aiwb2.wifi.status != WIFI_CONNECTED)
    {
        return true;
    }
    // 根据状态机执行对应操作
    switch (e->state)
    {
    case 0:
    {
        // 连接服务器
        a->recv_cnt = 0;
        e->printf(a, "AT+SOCKET=4,%s,%d\r\n", aiwb2.wifi.socket.host, aiwb2.wifi.socket.port);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 1:
    {
        // 等待连接成功，获取con id
        if (strstr((const char *)(e->recvbuf(a)), "OK\r\n"))
        {
            AT_DEBUG("<-%s", e->recvbuf(a));
            char *p_result = strtok(e->recvbuf(a), "\r\n");
            while (p_result != NULL)
            {
                if (strstr(p_result, "connect success") != NULL)
                {
                    sscanf(p_result, "connect success ConID=%d", &(aiwb2.wifi.socket.con_id));
                    e->state++;
                    break;
                }
                p_result = strtok(NULL, "\r\n");
            }
        }
        else if (e->is_timeout(a, 3000))
        {
            return true;
        }
    }
    break;
    case 2:
    {
        // 连接成功后，向对应的con id发送数据
        a->recv_cnt = 0;
        e->printf(a, "AT+SOCKETSEND=%d,%d\r\n", aiwb2.wifi.socket.con_id, send_bufsz);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 3:
    {
        // 超时或者等待>返回，直接发送数据
        if (e->is_timeout(a, 10000) || strstr((const char *)(e->recvbuf(a)), ">"))
        {
            AT_DEBUG("<-%s", e->recvbuf(a));
            a->recv_cnt = 0;
            for (int i = 0; i < send_bufsz; i++)
            {
                a->adap.write(send_buf + i, 1);
                printf("%02x", send_buf[i]);
            }
            printf("\r\n");
            // a->adap.write("\"\r\n", strlen("\"\r\n"));
            e->reset_timer(a);
            e->state++;
        }
        break;
    }
    case 4:
    {
        // 等待返回OK
        if (e->is_timeout(a, 15000) || (strstr((const char *)(e->recvbuf(a)), "OK") && (a->recv_cnt >= 2)))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            send_bufsz = 0;
            // return true;
            e->state++;
        }
        break;
    }
    case 5:
    {
        // 发送成功后，删除对应的SOCKET连接
        // 如果和MQTT同时使用的话，需要做这一步，下次发送重新创建SOCKET
        a->recv_cnt = 0;
        e->printf(a, "AT+SOCKETDEL=%d\r\n", aiwb2.wifi.socket.con_id);
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 6:
    {
        // 等待断开成功
        if (e->is_timeout(a, 15000) || strstr((const char *)(e->recvbuf(a)), "OK\r\n"))
        {
            AT_DEBUG("<-%s", e->recvbuf(a));
            return true;
            // e->state++;
        }
    }
    break;
    }
    return false;
}

/// @brief 获取SNTP时间
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int wifi_sntp_time_work(at_env_t *e)
{
    at_obj_t *a = &(aiwb2.at);
    static int rety_count = 0;
    // 根据状态机执行对应操作
    switch (e->state)
    {
    case 0:
    {
        // 连接服务器
        a->recv_cnt = 0;
        e->printf(a, "AT+SNTPTIMECFG=1,%d\r\n", *((int *)load_cfg(CFG_TIME_ZONE)));
        e->reset_timer(a);
        e->state++;
    }
    break;
    case 1:
    {
        // 等待连接成功，获取con id
        if (strstr((const char *)(e->recvbuf(a)), "OK\r\n"))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            rety_count = 1;
            e->reset_timer(a);
            e->state++;
        }
        else if (e->is_timeout(a, 3000))
        {
            return true;
        }
    }
    break;
    case 2:
    {
        if (e->is_timeout(a, 2000))
        {
            a->recv_cnt = 0;
            e->printf(a, "AT+SNTPTIME?\r\n");
            e->reset_timer(a);
            e->state++;
        }
    }
    break;
    case 3:
    {
        // 超时或者等待>返回，直接发送数据
        if (strstr((const char *)(e->recvbuf(a)), "OK\r\n"))
        {
            AT_DEBUG("<-%s\r\n", e->recvbuf(a));
            char month[5];
            struct tm date_tm;
            char *p_date = NULL;

            // 解析月份
            if ((p_date = strstr((const char *)(e->recvbuf(a)), "Jan")) != NULL)
            {
                date_tm.tm_mon = 1;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Feb")) != NULL)
            {
                date_tm.tm_mon = 2;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Mar")) != NULL)
            {
                date_tm.tm_mon = 3;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Apr")) != NULL)
            {
                date_tm.tm_mon = 4;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "May")) != NULL)
            {
                date_tm.tm_mon = 5;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Jun")) != NULL)
            {
                date_tm.tm_mon = 6;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Jul")) != NULL)
            {
                date_tm.tm_mon = 7;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Aug")) != NULL)
            {
                date_tm.tm_mon = 8;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Sep")) != NULL)
            {
                date_tm.tm_mon = 9;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Oct")) != NULL)
            {
                date_tm.tm_mon = 10;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Nov")) != NULL)
            {
                date_tm.tm_mon = 11;
            }
            else if ((p_date = strstr((const char *)(e->recvbuf(a)), "Dec")) != NULL)
            {
                date_tm.tm_mon = 12;
            }
            else
            {
                rety_count--;
                if (rety_count)
                {
                    e->state = 2;
                    break;
                }
                else
                {
                    return true;
                }
            }

            // 解析日期和时间
            sscanf(p_date, "%s %d %d:%d:%d %d\r\n",
                   month,
                   &(date_tm.tm_mday),
                   &(date_tm.tm_hour),
                   &(date_tm.tm_min),
                   &(date_tm.tm_sec),
                   &(date_tm.tm_year));

            {
                int date_invalid = 0;

                // 检查日期是否有效
                if (date_tm.tm_year < 2024)
                {
                    date_invalid = 1;
                }
                else if (date_tm.tm_mon < 1 || date_tm.tm_mon > 12)
                {
                    date_invalid = 1;
                }
                else if (date_tm.tm_mday < 1 || date_tm.tm_mday > 31)
                {
                    date_invalid = 1;
                }
                else if (date_tm.tm_hour < 0 || date_tm.tm_hour > 23)
                {
                    date_invalid = 1;
                }
                else if (date_tm.tm_min < 0 || date_tm.tm_min > 59)
                {
                    date_invalid = 1;
                }
                else if (date_tm.tm_sec < 0 || date_tm.tm_sec > 59)
                {
                    date_invalid = 1;
                }

                if (date_invalid)
                {
                    rety_count--;
                    if (rety_count)
                    {
                        e->state = 2;
                        break;
                    }
                    else
                    {
                        return true;
                    }
                }
            }

            // 调整年份和月份
            date_tm.tm_year -= 1900;
            date_tm.tm_mon -= 1;

            // 打印时间和设置RTC时间
            printf("time %04d-%02d-%02d %02d:%02d:%02d\r\n",
                   date_tm.tm_year,
                   date_tm.tm_mon,
                   date_tm.tm_mday,
                   date_tm.tm_hour,
                   date_tm.tm_min,
                   date_tm.tm_sec);
            rtc_time_set(rtc_timestamp_from_tm(&date_tm) - ((*((int *)load_cfg(CFG_TIME_ZONE))) * 3600));
            e->reset_timer(a);
            aiwb2.wifi.sntp_ok = 1;
            return true;
        }
        if (e->is_timeout(a, 3000))
        {
            return true;
        }
        break;
    }
    }
    return false;
}

/// @brief 模块初始化配置
/// @param e
/// @return true - 退出状态机, false - 保持状态机,
static int aiwb2_at_init_work(at_env_t *e)
{
    at_obj_t *a = (at_obj_t *)e->params;
    switch (e->state)
    {
    case 0: // 关闭WIFI电源
        e->reset_timer(a);
        e->state++;
        break;
    case 1:
        if (e->is_timeout(a, 10)) // 延时等待500ms
            e->state++;
        break;
    case 2:
        // if (aiwb2.test.enable == 0)
        // {
        //     // if ((*(int *)load_cfg(CFG_WIFI_EN)) == 1)
        //     // {
        //     //     aiwb2.wifi.enable = 1;
        //     //     snprintf(at_send_buf, sizeof(at_send_buf), "AT+WMODE=1,1\r\n");
        //     //     aiwb2_at_send(at_send_buf, "OK\r\n", wifi_set_mode_callback, 3, 3000);
        //     // }
        //     // else
        //     {
        //         at_send_multiline(&(aiwb2.at), ble_init_cmds_callback, ble_init_cmds);
        //     }
        // }
        // else
        // {
        //     at_send_multiline(&(aiwb2.at), ble_init_cmds_callback, ble_production_init_cmds);
        // }
        aiwb2.ble.config_step = 0;
        ble_config_callback(NULL);
        e->state++;
        break;
    case 3:
        return true;
    }

    return false;
}

/*
 * @brief   重启任务状态机
 * @return  true - 退出状态机, false - 保持状态机,
 */
static int aiwb2_reset_work(at_env_t *e)
{
    at_obj_t *a = (at_obj_t *)e->params;
    static uint8_t reset_count = 0;
    static uint32_t reset_delay = 0;
    switch (e->state)
    {
    case 0: // 关闭WIFI电源
        if(IS_TIMEOUT(reset_delay))
        {
            reset_count = 0;
            reset_delay = 0;
        }
        if(reset_delay == 0)
        {
            reset_delay = SET_SEC_DELAY(60 * 10);
        }
        reset_count++;
        if(reset_count > 3)
        {
            // system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        aiwb2_close();
        at_obj_init(&(aiwb2.at), &(aiwb2.at_adapter));
        memset(urc_buf, 0, sizeof(urc_buf));
        memset(recv_buf, 0, sizeof(recv_buf));
        {
            uint8_t data = 0;
            while (wifi_bt_uart_read(&data, 1) == 1)
                ;
        }
        aiwb2.ready = 0;
        memset(&(aiwb2.ble), 0, sizeof(aiwb2.ble));
        memset(&(aiwb2.wifi), 0, sizeof(aiwb2.wifi));
        // memset(&(aiwb2.test), 0, sizeof(aiwb2.test));
        memset(aiwb2.firmware_ver, 0, sizeof(aiwb2.firmware_ver));
        memset(aiwb2.sdk_ver, 0, sizeof(aiwb2.sdk_ver));

#if 0 // 是否启用蓝牙空闲自动重启
        aiwb2.ble.reset_en = *((int *)load_cfg(CFG_BLE_RESET_EN));
#endif
        e->reset_timer(a);
        e->state++;
        break;
    case 1:
        if (e->is_timeout(a, 1000)) // 延时等待500ms
            e->state++;
        break;
    case 2:
        wifi_bt_uart_rx_clear();
        aiwb2_open(); // 重启启动wifi
#if 0                 // 是否启用蓝牙空闲自动重启
        if (aiwb2.ble.reset_en)
        {
            aiwb2.ble.disconnect_timeout = get_tick() + (*((int *)load_cfg(CFG_BLE_RESET_IDLE))) * 1000;
        }
#endif
        e->state++;
        break;
    case 3:
        if (e->is_timeout(a, 10000) || aiwb2.ready) // 大约延时等待10s至wifi启动
        {
            // aiwb2.ready = 1;
            return true;
            // if (aiwb2.ready == 1)
            // {
            //     return true;
            // }
            // else
            // {
            //     e->state = 0;
            // }
        }
        break;
    }
    return false;
}

/*
 * @brief    初始化回调
 */
static void aiwb2_init_callbatk(at_response_t *r)
{
    if (r->ret == AT_RET_OK)
    {
        printf("aiwb2 Initialization successfully...\r\n");
        printf("aiwb2      ble mac: %s\r\n", aiwb2.ble.mac);
        printf("aiwb2     wifi mac: %s\r\n", aiwb2.wifi.mac);
        printf("aiwb2      sdk ver: %s\r\n", aiwb2.sdk_ver);
        printf("aiwb2 firmware ver: %s\r\n", aiwb2.firmware_ver);
    }
    else
        printf("aiwb2 Initialization failure...\r\n");
}

/// @brief 蓝牙处理JSON数据回调
/// @param data 数据
/// @param size 数据大小
static void ble_data_json_handler_callback(const uint8_t *data, int size)
{
    cJSON *w_json = (cJSON *)data;
    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
            else
            {
                free(trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
    }
}

/// @brief 蓝牙处理16进制数据回调
/// @param data 数据
/// @param size 数据大小
static void ble_data_hex_handler_callback(const uint8_t *data, int size)
{
    aiwb2_trans_t *trans_hex = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
    alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_hex);
    if (trans_hex != NULL)
    {
        trans_hex->trans_len = size;
        if (trans_hex->trans_len)
        {
            memcpy(trans_hex->trans_buf, data, trans_hex->trans_len);
            at_do_work(&(aiwb2.at), ble_send_hex_data_work, trans_hex);
        }
        else
        {
            free(trans_hex);
        }
    }
    else
    {
        printf("[%s]error: malloc failed\r\n", __FUNCTION__);
        system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
    }
}

/// @brief 蓝牙断开回调
/// @param r AT指令返回的句柄
static void ble_disconnect_callback(at_response_t *r)
{
    aiwb2.ble.status = BLE_DISCONNECTED;
}

/// @brief 蓝牙连接成功回调，产测用
/// @param r AT指令返回的句柄
static void ble_connect_callback(at_response_t *r)
{
    at_send_singlline(&aiwb2.at, ble_disconnect_callback, "AT+BLEDISCON\r\n");
    aiwb2.test.result = 1;
}

static void ble_config_callback(at_response_t *r)
{
    if (r->ret == AT_RET_OK || r == NULL)
    {
        switch (aiwb2.ble.config_step)
        {
        case 0:
        {
            if (aiwb2.test.enable == 0)
            {
                at_send_multiline(&(aiwb2.at), ble_config_callback, ble_init_cmds);
            }
            else
            {
                at_send_multiline(&(aiwb2.at), ble_config_callback, ble_production_init_cmds);
            }
            aiwb2.ble.config_step++;
        }
        break;
        case 1:
        {
            if (aiwb2.test.enable == 1)
            {
                snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLEMODE?\r\n");
                aiwb2_at_send(at_send_buf, "OK\r\n", ble_config_callback, 3, 3000);
                aiwb2.ble.config_step = 4;
            }
            else
            {
                static char at_ble_name[64] = {0};
                memcpy(at_ble_name, load_cfg(CFG_BLE_NAME), 32);
                if (strlen(at_ble_name) == 0)
                {
                    if (strncmp(BLE_ADV_NAME_PREFIX, "Pillow-ZS_", strlen(BLE_ADV_NAME_PREFIX)) == 0)
                    {
                        snprintf(at_ble_name, sizeof(at_ble_name), "AT+BLENAME=%s%c%c%c%c\r\n",
                                 BLE_ADV_NAME_PREFIX, aiwb2.ble.mac[8], aiwb2.ble.mac[9], aiwb2.ble.mac[10], aiwb2.ble.mac[11]);
                    }
                    else
                    {
                        snprintf(at_ble_name, sizeof(at_ble_name), "AT+BLENAME=%s%s\r\n", BLE_ADV_NAME_PREFIX, aiwb2.ble.mac);
                    }
                    // save_cfg(CFG_BLE_NAME, at_ble_name, 32);
                }
                else
                {
                    snprintf(at_ble_name, sizeof(at_ble_name), "AT+BLENAME=%s\r\n", (char *)load_cfg(CFG_BLE_NAME));
                }
                at_send_singlline(&(aiwb2.at), ble_config_callback, at_ble_name);
                aiwb2.ble.config_step++;
            }
        }
        break;
        case 2:
        {
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLEADVDATA=%s\r\n", aiwb2.ble.mac);
            aiwb2_at_send(at_send_buf, "OK\r\n", ble_config_callback, 3, 3000);
            printf("set ble name success\r\n");
            aiwb2.ble.config_step++;
        }
        break;
        case 3:
        {
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLEMODE?\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", ble_config_callback, 3, 3000);
            printf("set adv data success\r\n");
            aiwb2.ble.config_step++;
        }
        break;
        case 4:
        {
            int ble_mode = -1;
            sscanf(r->recvbuf, "\r\n+BLEMODE:%d\r\n", &ble_mode);
            if (aiwb2.test.enable == 0)
            {
                if (ble_mode != 0)
                {
                    snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLEMODE=0\r\n");
                    aiwb2_at_send(at_send_buf, "OK\r\n", ble_config_callback, 3, 3000);
                }
            }
            else
            {
                if (ble_mode != 1)
                {
                    snprintf(at_send_buf, sizeof(at_send_buf), "AT+BLEMODE=1\r\n");
                    aiwb2_at_send(at_send_buf, "OK\r\n", ble_config_callback, 3, 3000);
                }
            }
            aiwb2.ble.config_step++;
        }
        break;
        case 5:
        {
            if (aiwb2.test.enable == 0)
            {
                if ((*(int *)load_cfg(CFG_WIFI_EN)) == 1)
                {
                    aiwb2.wifi.enable = 1;
                    aiwb2.wifi.config_step = 0;
                    // aiwb2.wifi.rety_delay = SET_SEC_DELAY(120);
                    // snprintf(at_send_buf, sizeof(at_send_buf), "AT+WMODE=0,0\r\n");
                    // aiwb2_at_send(at_send_buf, "OK\r\n", wifi_set_mode_close_callback, 3, 3000);
                    aiwb2.wifi.rety_delay = SET_SEC_DELAY(120);
                    wifi_config_callback(NULL);
                }
                else
                {
                    aiwb2_init_callbatk(r);
                }
            }
            else
            {
                aiwb2_init_callbatk(r);
                at_send_singlline(&(aiwb2.at), NULL, "AT+BLESCAN\r\n");
                aiwb2.test.ble_scan_count = 1;
            }
        }
        break;
        }
    }
}

/// @brief WIFI处理JSON数据回调
/// @param data 数据
/// @param size 数据大小
static void wifi_data_json_handler_callback(const uint8_t *data, int size)
{
    cJSON *w_json = (cJSON *)data;
    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                if(at_do_work(&(aiwb2.at), mqtt_send_data_work, trans_json) == NULL)
                {
                    printf("%s:at list full, send abort\r\n",__FUNCTION__);
                    free(trans_json);
                }
            }
            else
            {
                free(trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
    }
}

static void wifi_config_callback(at_response_t *r)
{
    if (r->ret == AT_RET_OK || r == NULL)
    {
        switch(aiwb2.wifi.config_step)
        {
            case 0:
            {
                snprintf(at_send_buf, sizeof(at_send_buf), "AT+WMODE=0,0\r\n");
                aiwb2_at_send(at_send_buf, "OK\r\n", wifi_config_callback, 3, 3000);
                aiwb2.wifi.config_step++;
            }
            break;
            case 1:
            {
                snprintf(at_send_buf, sizeof(at_send_buf), "AT+WMODE=1,0\r\n");
                aiwb2_at_send(at_send_buf, "OK\r\n", wifi_config_callback, 3, 3000);
                aiwb2.wifi.config_step++;
            }
            break;
            case 2:
            {
                aiwb2.wifi.mode = WIFI_MODE_STA;
                snprintf(at_send_buf, sizeof(at_send_buf), "AT+SOCKETRECVCFG=1\r\n");
                aiwb2_at_send(at_send_buf, "OK\r\n", wifi_config_callback, 3, 3000);
                aiwb2.wifi.config_step++;
            }
            break;
            case 3:
            {
                snprintf(at_send_buf, sizeof(at_send_buf), "AT+WJAP=\"%s\",\"%s\"\r\n", (char *)load_cfg(CFG_WIFI_SSID), (char *)load_cfg(CFG_WIFI_PWD));
                aiwb2.wifi.status = WIFI_CONNECTING;
                aiwb2_at_send(at_send_buf, "OK\r\n", wifi_config_callback, 3, 30000);
                aiwb2.wifi.config_step++;
            }
            break;
            case 4:
            {
                aiwb2_init_callbatk(r);
            }
            break;
        }
        
    }
    if(r->ret != AT_RET_OK)
    {
        switch(aiwb2.wifi.config_step)
        {
            case 4:
            {
                int err_code = 0;
                sscanf(r->recvbuf, "\r\n+WJAP:%d\r\n", &err_code);
                printf("wifi connect error,code(%d), ssid(%s), pwd(%s)\r\n", err_code, (char *)load_cfg(CFG_WIFI_SSID), (char *)load_cfg(CFG_WIFI_PWD));
                aiwb2.wifi.status = WIFI_DISCONNECTED;
            }
            break;
        }
    }
}

/// @brief WIFI获取到IP回调
/// @param r AT指令返回的句柄
static void wifi_got_ip_callback(at_response_t *r)
{
    if (r->ret == AT_RET_OK)
    {
        // sscanf(r->recvbuf, "\r\n+WJAP:%d,%s,%s,%s\r\n",aiwb2.wifi.dhcp, aiwb2.wifi.ipv4, aiwb2.wifi.mask, aiwb2.wifi.gateway);
        char *p = strtok(r->recvbuf, ",");
        p = strtok(NULL, ",");
        printf("WIFI SSID:      %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI PWD:       %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI BSSID:     %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI Security:  %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI MAC:       %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI CH:        %s\r\n", p);
        p = strtok(NULL, ",");
        printf("WIFI IP:        %s\r\n", p);
        memcpy(aiwb2.wifi.ipv4, p, strlen(p) + 1);
        p = strtok(NULL, "\r\n");
        printf("WIFI GATEWAY:   %s\r\n", p);
        memcpy(aiwb2.wifi.gateway, p, strlen(p) + 1);

        // aiwb2_at_send("AT+WRSSI?\r\n", "OK\r\n", wifi_got_rssi_callback, 3, 3000);

        aiwb2.wifi.mqtt.config_step = 0;
        wifi_mqtt_config_callback(r);
    }
}

static void wifi_got_rssi_callback(at_response_t *r)
{
    if (r->ret == AT_RET_OK)
    {
        sscanf(r->recvbuf, "\r\n+WRSSI:%d\r\n", &(aiwb2.wifi.rssi));
        printf("WIFI RSSI:      %d\r\n", aiwb2.wifi.rssi);
        // aiwb2.wifi.mqtt.config_step = 0;
        // wifi_mqtt_config_callback(r);
    }
}

/// @brief MQTT配置和连接回调
/// @param r AT指令返回的句柄
static void wifi_mqtt_config_callback(at_response_t *r)
{
    // 如果第一次返回OK，则表示配置服务器地址成功
    if (r->ret == AT_RET_OK || r == NULL)
    {
        switch (aiwb2.wifi.mqtt.config_step)
        {
        case 0: // 设置服务器地址
        {
#if MQTT_TEST == 1
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=1,*************\r\n");
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=1,%s\r\n", (char*)load_cfg(CFG_HOST));
#else
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=1,**************\r\n");
#endif
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=1,************\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 1: // 设置服务器端口
        {
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=2,%d\r\n", *((int *)load_cfg(CFG_PORT)));
#if MQTT_TEST == 1
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=2,1883\r\n");
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=2,%d\r\n", *((int *)load_cfg(CFG_PORT)));
#else
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=2,1883\r\n");
#endif
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=2,10253\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 2: // 设置连接方式：0：TCP，1：SSL
        {
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=3,1\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 3: // 设置Client ID
        {
#if MQTT_TEST == 1
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=4,\"%s\"\r\n", aiwb2.wifi.mac);
#else
            snprintf(at_send_buf,
                     sizeof(at_send_buf),
                     "AT+MQTT=4,\"%s|%s\"\r\n",
                     (char *)load_cfg(CFG_PRODUCT_KEY),
                     (char *)load_cfg(CFG_PRODUCT_SECRET));
#endif
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=4,\"%s|%s\"\r\n", "pillowtest", "pillowtest");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 4: // 设置用户名
        {
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=5,%s\r\n", (char *)load_cfg(CFG_MQ_USER));
#if MQTT_TEST == 1
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=5,\"user\"\r\n", aiwb2.wifi.mac);
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=5,%s\r\n", (char *)load_cfg(CFG_MQ_USER));
#else
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=5,\"%s\"\r\n", aiwb2.wifi.mac);
#endif
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=5,\"user\"\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 5: // 设置密码
        {
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=6,%s\r\n", (char *)load_cfg(CFG_MQ_PASSWORD));
#if MQTT_TEST == 1
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=6,\"user1234\"\r\n", aiwb2.wifi.mac);
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=6,%s\r\n", (char *)load_cfg(CFG_MQ_PASSWORD));
#else
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=6,\"123456\"\r\n");
#endif
            // snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=6,userpwd\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 6:
        {
            // 设置设置遗嘱消息， 格式为AT+MQTT=7,<LWT_topic>,<LWT_qos>,<LWT_Retained>,<LWTpayload>
            // LWT_topic：   遗嘱主题(不需要遗嘱这里设置为””)
            // LWT_qos：     遗嘱QOS(0/1/2)
            // LWT_Retained：遗嘱retained(0/1)
            // LWTpayload：  遗嘱消息内容
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT=7,\"\",0,0,\"\"\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
        }
        break;
        case 7: // 连接MQTT
        {
            snprintf(at_send_buf, sizeof(at_send_buf), "AT+MQTT\r\n");
            aiwb2_at_send(at_send_buf, "OK\r\n", wifi_mqtt_config_callback, 3, 3000);
            aiwb2.wifi.mqtt.config_step++;
            snprintf(aiwb2.wifi.mqtt.sub_topic,
                     sizeof(aiwb2.wifi.mqtt.sub_topic),
                     "\"/sys/%s/%s/thing/service/post\"",
                     (char *)load_cfg(CFG_PRODUCT_KEY),
                     aiwb2.wifi.mac);
            // snprintf(aiwb2.wifi.mqtt.sub_topic,
            //         sizeof(aiwb2.wifi.mqtt.sub_topic),
            //         "\"/sys/pillowtest/%s/thing/service/post\"",
            //         aiwb2.wifi.mac);
            snprintf(aiwb2.wifi.mqtt.pub_topic,
                     sizeof(aiwb2.wifi.mqtt.pub_topic),
                     "\"/sys/%s/%s/thing/property/post\"",
                     (char *)load_cfg(CFG_PRODUCT_KEY),
                     aiwb2.wifi.mac);
            // snprintf(aiwb2.wifi.mqtt.pub_topic,
            //         sizeof(aiwb2.wifi.mqtt.pub_topic),
            //         "\"/sys/pillowtest/%s/thing/property/post\"",
            //         aiwb2.wifi.mac);
        }
        break;
        case 8:
        {
            aiwb2.wifi.mqtt.rety_delay = SET_SEC_DELAY(120);
        }
        break;
        }
    }
}

static void wifi_mqtt_subscribe_callback(at_response_t *r)
{
    if (r->ret == AT_RET_OK)
    {
        printf("MQTT SUB TOPIC(%s) OK\r\n", aiwb2.wifi.mqtt.sub_topic);
    }
#if WIFI_AUTO_REALTIME_TEST == 1
        aiwb2_wifi_set_real_time(1, 
                    NULL, 
                    0, 
                    120, 
                    0, 
                    0,
                    0);
        real_time_enable(REAL_TIME_WIFI);
#endif
}

/*
 * @brief   aiwb2 通信异常处理
 */
static void aiwb2_at_error(void)
{
    printf("aiwb2 AT communication error\r\n");
    if (aiwb2.ble.wait_wifi == 1)
    {
        push_wifi_status_handler(WIFI_STATUS_WJAP_FAIL);
        aiwb2.ble.wait_wifi = 0;
    }
    if (aiwb2.ble.wait_wifi == 2)
    {
        push_wifi_status_handler(WIFI_STATUS_MQTT_FAIL);
        aiwb2.ble.wait_wifi = 0;
    }
    else if (aiwb2.err_to_init == 1)
    {
        // 执行重启作业
        // at_do_work(&(aiwb2.at), aiwb2_at_check_work, &(aiwb2.at));
        printf("aiwb2 at error, reset\r\n");
        at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
        aiwb2.err_to_init = 0;
    }
}

/*
 * @brief    自定义AT发送器
 */
static void aiwb2_at_sender(at_env_t *e)
{
    e->printf(&(aiwb2.at), (char *)e->params);
}

/// @brief at发送创建
/// @param cmd AT指令
/// @param prefix 需要监测返回的前缀
/// @param cb 返回处理
/// @param retry 重试次数
/// @param timeout 超时时间
static void aiwb2_at_send(char *cmd, char *prefix, at_callbatk_t cb, uint8_t retry, uint16_t timeout)
{
    aiwb2.at_cmd.sender = aiwb2_at_sender;
    aiwb2.at_cmd.matcher = prefix;
    aiwb2.at_cmd.cb = cb;
    aiwb2.at_cmd.retry = retry;
    aiwb2.at_cmd.timeout = timeout;

    at_do_cmd(&(aiwb2.at), cmd, &(aiwb2.at_cmd));
}

/// @brief gpio初始化
/// @param
static void aiwb2_gpio_init(void)
{
    gpio_init_type gpio_init_struct;

    crm_periph_clock_enable(AIWB2_EN_CRM_CLK, TRUE);

    gpio_default_para_init(&gpio_init_struct);

    /* configure the uart tx pin */
    gpio_init_struct.gpio_drive_strength = GPIO_DRIVE_STRENGTH_STRONGER;
    gpio_init_struct.gpio_out_type = GPIO_OUTPUT_PUSH_PULL;
    gpio_init_struct.gpio_mode = GPIO_MODE_OUTPUT;
    gpio_init_struct.gpio_pins = AIWB2_EN_PIN;
    gpio_init_struct.gpio_pull = GPIO_PULL_NONE;
    gpio_init(AIWB2_EN_GPIO, &gpio_init_struct);

    gpio_bits_reset(AIWB2_EN_GPIO, AIWB2_EN_PIN);
}

/// @brief 推送JSON监测状态
/// @param params 各种状态
/// @param recv_cb
/// @return
uint8_t push_real_time_status_handler(params_t params)
{
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    cJSON *w_json = ble_make_real_time_status_package(&params);

    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
            else
            {
                free(trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

/// @brief 推送JSON监测数据
/// @param cmd 指令
/// @param data_buffer 数据
/// @param data_size 数据大小
/// @param recv_cb
/// @return
uint8_t push_real_time_data_handler(char *cmd, uint16_t *data_buffer, uint32_t data_size)
{
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    cJSON *w_json = ble_make_real_time_data_package(cmd, data_buffer, data_size);

    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
            else
            {
                free(trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

/**
 * @brief 推送实时监测数据到WIFI和蓝牙
 * @param params 包含当前各种监测参数的结构体
 * @param hb_buf 心率数据缓冲区
 * @param hb_size 心率数据大小
 * @param br_buf 呼吸数据缓冲区
 * @param br_size 呼吸数据大小
 * @return 1-发送成功,0-发送失败
 */
uint8_t push_real_time_all_handler(params_t params, uint8_t *hb_buf, uint32_t hb_size, uint8_t *br_buf, uint32_t br_size)
{
    static int heartbeat = 0;
    static int heartbeat_count = 0;
    static int breath = 0;
    static int breath_count = 0;
    static int move = 0;
    static int snore = 0;
    // 如果AT指令执行器正忙,则返回失败
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    if (aiwb2.wifi.real_time.delay != 0 &&
        aiwb2.wifi.enable == 1 &&
        !IS_TIMEOUT(aiwb2.wifi.real_time.timer) &&
        aiwb2.wifi.status == WIFI_CONNECTED)
    {
        if (params.heartbeat != 0)
        {
            heartbeat += params.heartbeat;
            heartbeat_count++;
        }
        if (params.breath != 0)
        {
            breath += params.breath;
            breath_count++;
        }
        if (aiwb2.wifi.real_time.delay <= 1)
        {
            move += params.move_factor;
        }
        else
        {
            move += params.move_factor;
        }
        snore += params.snore;
    }

    // WIFI数据上传处理
    if (aiwb2.wifi.real_time.delay != 0 &&
        aiwb2.wifi.enable == 1 &&
        aiwb2.wifi.status == WIFI_CONNECTED &&
        IS_TIMEOUT(aiwb2.wifi.real_time.timer))
    {
        // 设置下次上传定时器
        aiwb2.wifi.real_time.timer = SET_SEC_DELAY(aiwb2.wifi.real_time.delay);

        // 分配数据传输缓冲区
        aiwb2_trans_t *trans_data = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_data);
        if (trans_data == NULL)
        {
            printf("push real time hex handler alloc failed\r\n");
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
            return 0;
        }
        if (params.sleep_status == STATUS_LEAVE)
        {
            params.heartbeat = 0;
            params.breath = 0;
            params.snore = 0;
            params.move_factor = 0;
        }
        else
        {
            if (heartbeat_count == 0 || breath_count == 0)
            {
                params.heartbeat = 0;
                params.breath = 0;
                params.snore = 0;
                params.move_factor = 0;
                params.sleep_status = STATUS_LEAVE;
            }
            else
            {
                params.heartbeat = heartbeat / heartbeat_count;
                params.breath = breath / breath_count;
                params.snore = snore;
                params.move_factor = move;
            }
        }
        heartbeat = 0;
        heartbeat_count = 0;
        breath = 0;
        breath_count = 0;
        move = 0;
        snore = 0;
        // TCP模式数据上传
        if (aiwb2.wifi.real_time.mode == 0)
        {
            // 生成JSON格式数据包
            cJSON *w_json = wifi_make_real_time_data_package(params, hb_buf, hb_size, br_buf, br_size);
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_data->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_data->trans_len = strlen((char *)trans_data->trans_buf);
                memcpy(&aiwb2.wifi.socket, &aiwb2.wifi.real_time.socket, sizeof(aiwb2.wifi.real_time.socket));
                // 执行TCP发送任务
                // at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data);
                if(at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data) == NULL)
                {
                    printf("%s:at list full, send abort\r\n",__FUNCTION__);
                    free(trans_data);
                }
            }
            else
            {
                free(trans_data);
            }
            cJSON_Delete(w_json);
        }
        // MQTT模式数据上传
        else
        {
            cJSON *w_json = NULL;
            // 根据upload标志决定是否上传完整数据
            if (aiwb2.wifi.real_time.upload == 1)
            {
                w_json = wifi_make_real_time_data_package(params, hb_buf, hb_size, br_buf, br_size);
            }
            else
            {
                w_json = wifi_make_real_time_data_package(params, NULL, 0, NULL, 0);
            }
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_data->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_data->trans_len = strlen((char *)trans_data->trans_buf);
                // at_do_work(&(aiwb2.at), mqtt_send_data_work, trans_data);
                if(at_do_work(&(aiwb2.at), mqtt_send_data_work, trans_data) == NULL)
                {
                    printf("%s:at list full, send abort\r\n",__FUNCTION__);
                    free(trans_data);
                }
            }
            else
            {
                free(trans_data);
            }
            cJSON_Delete(w_json);
        }
    }
    if (aiwb2.ble.status == BLE_CONNECTED)
    {
        aiwb2_trans_t *trans_data = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_data);
        if (trans_data == NULL)
        {
            printf("push real time hex handler alloc failed\r\n");
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
            return 0;
        }
        trans_data->trans_len = ble_make_real_time_all_package(params, hb_buf, hb_size, br_buf, br_size, trans_data->trans_buf);
        if (trans_data->trans_len != 0)
        {
            at_do_work(&(aiwb2.at), ble_send_hex_data_work, trans_data);
        }
        else
        {
            free(trans_data);
        }
    }

    return 1;
}

/// @brief 推送颈椎舒展状态
/// @param recv_cb
/// @return
uint8_t push_get_massage_handler(void)
{
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    cJSON *w_json = ble_make_get_massage_package();

    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed1\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

/// @brief 发送气泵测试状态
/// @param recv_cb
/// @return
uint8_t push_get_pump_test_handler(void)
{
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    cJSON *w_json = ble_make_get_pump_test_package();

    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

uint8_t push_wifi_status_handler(uint8_t status)
{
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    cJSON *w_json = ble_make_push_wifi_status_package(status);

    if (w_json != NULL)
    {
        aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
        if (trans_json != NULL)
        {
            uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
            if (ret)
            {
                trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                at_do_work(&(aiwb2.at), ble_send_data_work, trans_json);
            }
        }
        else
        {
            printf("[%s]error: malloc failed\r\n", __FUNCTION__);
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

uint8_t push_raw_upload_handler(int16_t *raw_buffer, uint32_t buffer_size)
{
    // 如果AT指令执行器正忙,则返回失败
    if (aiwb2.at.cursor != NULL)
    {
        return 0;
    }

    if (aiwb2.wifi.raw_upload.enable == 0)
    {
        return 0;
    }
    // TCP模式数据上传
    if (aiwb2.wifi.real_time.mode == 0)
    {
        // 生成JSON格式数据包
        cJSON *w_json = wifi_make_raw_upload_package(raw_buffer, buffer_size);
        aiwb2_trans_t *trans_data = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_data);
        aiwb2_trans_t *trans_data2 = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
        alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_data2);
        if (trans_data == NULL)
        {
            printf("push real time hex handler alloc failed\r\n");
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
            return 0;
        }
        if (trans_data2 == NULL)
        {
            printf("push real time hex handler2 alloc failed\r\n");
            system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
            return 0;
        }
        uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_data->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
        if (ret)
        {
            int json_len = strlen((char *)trans_data->trans_buf);
            memcpy(trans_data2->trans_buf, trans_data->trans_buf + json_len / 2, json_len - json_len / 2);
            trans_data->trans_buf[json_len / 2] = NULL;
            trans_data->trans_len = json_len / 2;
            trans_data2->trans_len = json_len - json_len / 2;
            memcpy(&aiwb2.wifi.socket, &aiwb2.wifi.raw_upload.socket, sizeof(aiwb2.wifi.raw_upload.socket));
            // 执行TCP发送任务
            // at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data);
            if(at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data) == NULL)
            {
                printf("%s:at list full, send abort\r\n",__FUNCTION__);
                free(trans_data);
            }
            // at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data2);
            if(at_do_work(&(aiwb2.at), tcp_send_data_work, trans_data2) == NULL)
            {
                printf("%s:at list full, send abort\r\n",__FUNCTION__);
                free(trans_data2);
            }
        }
        else
        {
            free(trans_data);
            free(trans_data2);
        }
        cJSON_Delete(w_json);
    }

    return 1;
}

/// @brief 检查蓝牙鉴权是否成功
/// @param
static void aiwb2_ble_check_auth(void)
{
    if (aiwb2.ble.status == BLE_CONNECTED)
    {
        if (aiwb2.ble.auth == 0 && aiwb2.ble.auth_timeout && IS_TIMEOUT(aiwb2.ble.auth_timeout))
        {
            // at_send_singlline(&(aiwb2.at), NULL, "AT+BLEDISCON\r\n");
            at_do_work(&aiwb2.at, ble_disconnect_work, NULL);
            // aiwb2.ble.status = BLE_DISCONNECTED;
            aiwb2.ble.auth = 0;
            aiwb2.ble.auth_timeout = 0;
        }
    }
}

/// @brief 蓝牙断开后，检查是否需要复位模组
/// @param
static void aiwb2_ble_check_reset(void)
{
#if 0 // 是否启用蓝牙自动重启
    aiwb2.ble.reset_en = *((int *)load_cfg(CFG_BLE_RESET_EN));
    if (aiwb2.ble.reset_en)
    {
        if (aiwb2.ble.disconnect_timeout)
        {
            if (IS_TIMEOUT(aiwb2.ble.disconnect_timeout))
            {
                memset(urc_buf, 0, sizeof(urc_buf));
                memset(recv_buf, 0, sizeof(recv_buf));
                {
                    uint8_t data = 0;
                    while (wifi_bt_uart_read(&data, 1) == 1)
                        ;
                }
                at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
            }
        }
        if (aiwb2.ble.idle_timeout)
        {
            if (IS_TIMEOUT(aiwb2.ble.disconnect_timeout))
            {
                memset(urc_buf, 0, sizeof(urc_buf));
                memset(recv_buf, 0, sizeof(recv_buf));
                {
                    uint8_t data = 0;
                    while (wifi_bt_uart_read(&data, 1) == 1)
                        ;
                }
                at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
            }
        }
    }
#endif
}

/// @brief 模组初始化
/// @param
void aiwb2_init(void)
{
    wifi_bt_uart_init(115200);
    aiwb2_gpio_init();

    /*
     * @brief   AT适配器
     */
    aiwb2.at_adapter.write = wifi_bt_uart_write;
    aiwb2.at_adapter.read = wifi_bt_uart_read;
    aiwb2.at_adapter.error = aiwb2_at_error;
    aiwb2.at_adapter.utc_tbl = (utc_item_t *)urc_table;
    aiwb2.at_adapter.urc_buf = urc_buf;
    aiwb2.at_adapter.recv_buf = recv_buf;
    aiwb2.at_adapter.urc_tbl_count = sizeof(urc_table) / sizeof(urc_table[0]);
    aiwb2.at_adapter.urc_bufsize = sizeof(urc_buf);
    aiwb2.at_adapter.recv_bufsize = sizeof(recv_buf);

    aiwb2.ble.status = BLE_DISCONNECTED;

    at_obj_init(&(aiwb2.at), &(aiwb2.at_adapter));
    memset(urc_buf, 0, sizeof(urc_buf));
    memset(recv_buf, 0, sizeof(recv_buf));
    {
        uint8_t data = 0;
        while (wifi_bt_uart_read(&data, 1) == 1)
            ;
    }
    // 启动WIFI
    at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
}

void aiwb2_ble_proc(void)
{
#if BLE_NOT_AUTH == 0
    aiwb2_ble_check_auth();
#endif
    aiwb2_ble_check_reset();

    if (IS_TIMEOUT(aiwb2.ble.wait_wifi_delay) && aiwb2.ble.wait_wifi == 2)
    {
        push_wifi_status_handler(WIFI_STATUS_MQTT_FAIL);
        aiwb2.ble.wait_wifi = 0;
    }
}

static void aiwb2_wifi_heartbeat_callback(char *result, void *params)
{
    printf("send and receive heartbeat\r\n");
    aiwb2.wifi.mqtt.send_and_wait = MQTT_RECV;
}

static void aiwb2_wifi_upload_record_callback(char *result, void *params)
{
    printf("send and receive record\r\n");
    aiwb2.wifi.mqtt.send_and_wait = MQTT_RECV;
    sleep_record_get_finish();
}

static void aiwb2_wifi_send_and_wait(void)
{
    static uint32_t send_rety_count = 0;
    static cJSON *w_json = NULL;
    static uint32_t send_and_wait_delay = 0;
    static uint32_t heartbeat_delay = 0;
    static uint32_t record_delay = 0;
    if (aiwb2.wifi.mqtt.status != MQTT_CONNECTED)
    {
        heartbeat_delay = 0;
        record_delay = 0;
        send_and_wait_delay = 0;
        aiwb2.wifi.mqtt.send_and_wait = MQTT_IDLE;
        if (w_json)
        {
            cJSON_Delete(w_json);
            w_json = NULL;
        }
        return;
    }

    switch (aiwb2.wifi.mqtt.send_and_wait)
    {
    case MQTT_IDLE:
    {
        if (w_json == NULL)
        {
            if (IS_TIMEOUT(heartbeat_delay))
            {
                wifi_register_receive_callback(WSEND_HEARTBEAT_CB, aiwb2_wifi_heartbeat_callback, NULL);
                w_json = wifi_make_heartbeat_package();
                send_rety_count = 3;
            }
            else if (IS_TIMEOUT(record_delay))
            {
                // wifi_register_receive_callback(WSEND_RECORD_CB, aiwb2_wifi_upload_record_callback, NULL);
                // w_json = wifi_make_record_data_package();
                // send_rety_count = 3;
            }
        }

        if (w_json != NULL)
        {
            aiwb2_trans_t *trans_json = (aiwb2_trans_t *)calloc(1, sizeof(aiwb2_trans_t));
            alloc_printf("%s: alloc memory %08x\r\n", __FUNCTION__, (uint32_t)trans_json);
            if (trans_json != NULL)
            {
                uint8_t ret = cJSON_PrintPreallocated(w_json, (char *)trans_json->trans_buf, AIWB2_SEND_BUFFER_SIZE, 0);
                if (ret)
                {
                    trans_json->trans_len = strlen((char *)trans_json->trans_buf);
                    // at_do_work(&(aiwb2.at), mqtt_send_data_work, trans_json);
                    if(at_do_work(&(aiwb2.at), mqtt_send_data_work, trans_json) == NULL)
                    {
                        printf("%s:at list full, send abort\r\n",__FUNCTION__);
                        free(trans_json);
                    }
                    aiwb2.wifi.mqtt.send_and_wait = MQTT_WAIT;
                    send_and_wait_delay = SET_SEC_DELAY(60);
                }
                else
                {
                    free(trans_json);
                }
            }
            else
            {
                system_reset_with_flag(RESET_FLAG_EXCEPTION, RESET_DATA_NUMBER);
            }
            // cJSON_Delete(w_json);
        }
    }
    break;
    case MQTT_WAIT:
    {
#if WIFI_AUTO_REALTIME_TEST == 1
        aiwb2.wifi.mqtt.send_and_wait = MQTT_RECV;
#endif
        if (IS_TIMEOUT(send_and_wait_delay))
        {
            printf("===============wifi error===============\r\n");
            printf("can not receive\r\n");
            aiwb2.wifi.mqtt.send_and_wait = MQTT_TIMEOUT;
        }
    }
    break;
    case MQTT_RECV:
    {
        send_rety_count = 0;
        if (w_json)
        {
            cJSON_Delete(w_json);
            w_json = NULL;
        }
#if USE_RECORD_POINT == 1
        sleep_record_wifi_point_update();
#endif
        heartbeat_delay = SET_SEC_DELAY(60 * 5);
        record_delay = SET_SEC_DELAY(1);
        aiwb2.wifi.mqtt.send_and_wait = MQTT_IDLE;
    }
    break;
    case MQTT_TIMEOUT:
    {
        int rand_delay = 1;
        if (send_rety_count)
        {
            rand_delay = (get_tick() % 6 + 1) * 10 / (send_rety_count + 1);
            send_rety_count--;
        }
        heartbeat_delay = SET_SEC_DELAY(rand_delay);
        record_delay = SET_SEC_DELAY(rand_delay + 5);
        if (send_rety_count == 0 && w_json)
        {
            cJSON_Delete(w_json);
            w_json = NULL;
            at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
            aiwb2.err_to_init = 0;
        }
        printf("Mqtt recv timeout, retry delay %d\r\n", rand_delay);
        aiwb2.wifi.mqtt.send_and_wait = MQTT_IDLE;
    }
    break;
    }
}

void aiwb2_wifi_proc(void)
{
    static uint32_t sntp_check_delay = 0;
    static uint32_t get_rssi_delay = 0;
    if (aiwb2.wifi.enable == 0)
    {
        sntp_check_delay = 0;
        get_rssi_delay = 0;
        aiwb2.wifi.rety_delay = 0;
        aiwb2.wifi.mqtt.rety_delay = 0;
        return;
    }   

    if (aiwb2.wifi.status == WIFI_DISCONNECTED)
    {
        if(aiwb2.wifi.rety_delay == 0)
        {
            aiwb2.wifi.rety_delay = SET_SEC_DELAY(60 * 5);
        }
        if (IS_TIMEOUT(aiwb2.wifi.rety_delay) && aiwb2.wifi.rety_delay)
        {
            aiwb2.wifi.rety_delay = SET_SEC_DELAY(300);
            printf("wifi connect timeout, aiwb2 reset\r\n");
            at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
        }
        
    }
    else if (aiwb2.wifi.status == WIFI_CONNECTED)
    {
        aiwb2.wifi.rety_delay = 0;
#if WIFI_AUTO_UPLOAD == 1
        aiwb2_wifi_send_and_wait();
#endif
        if (aiwb2.wifi.mqtt.status == MQTT_CONNECTED)
        {
            aiwb2.wifi.mqtt.rety_delay = 0;
        }
        else if (aiwb2.wifi.mqtt.status == MQTT_DISCONNECTED)
        {
            if(aiwb2.wifi.mqtt.rety_delay == 0)
            {
                aiwb2.wifi.mqtt.rety_delay = SET_SEC_DELAY(60 * 5);
            }
            if(aiwb2.wifi.mqtt.rety_delay &&
                 IS_TIMEOUT(aiwb2.wifi.mqtt.rety_delay))
            {
                aiwb2.wifi.mqtt.rety_delay = 0;
                printf("mqtt connect timeout, aiwb2 reset\r\n");
                at_do_work(&(aiwb2.at), aiwb2_reset_work, &(aiwb2.at));
            }
        }

        if (IS_TIMEOUT(sntp_check_delay))
        {
            if (aiwb2.wifi.sntp_ok == 0)
            {
                sntp_check_delay = SET_SEC_DELAY(10);
            }
            else
            {
                sntp_check_delay = SET_SEC_DELAY(3600);
            }
            at_do_work(&(aiwb2.at), wifi_sntp_time_work, NULL);
        }
        if (IS_TIMEOUT(get_rssi_delay) && get_rssi_delay)
        {
            get_rssi_delay = SET_SEC_DELAY(60);
            aiwb2_at_send("AT+WRSSI?\r\n", "OK\r\n", wifi_got_rssi_callback, 3, 3000);
        }
        if (get_rssi_delay == 0)
        {
            get_rssi_delay = SET_SEC_DELAY(10);
        }
    }
}

/// @brief 模组轮询
/// @param
void aiwb2_proc(void)
{
#if BLE_NOT_AUTH == 0
    aiwb2_ble_check_auth();
#endif
#if BAND_MODEL == 1
#endif
    aiwb2_ble_check_reset();
    aiwb2_ble_proc();
    aiwb2_wifi_proc();
    if (IS_TIMEOUT(aiwb2.hold_ms))
    {
        at_poll_task(&(aiwb2.at));
    }
}

/// @brief 获取蓝牙mac地址
/// @param
/// @return
char *get_ble_mac(void)
{
    return (char *)aiwb2.ble.mac;
}

/// @brief 设置蓝牙鉴权结果
/// @param
void aiwb2_ble_set_auth(void)
{
    aiwb2.ble.auth = 1;
    aiwb2.ble.auth_timeout = 0;
}

/// @brief 获取蓝牙鉴权结果
/// @param
/// @return 1：成功，0：失败
int aiwb2_ble_get_auth(void)
{
#if BLE_NOT_AUTH == 1
    return 1;
#else
    return aiwb2.ble.auth;
#endif
}

int aiwb2_ble_set_break_timeout(int ms)
{
    // ble_break_set = ms;
    return 0;
}

int aiwb2_ble_set_idle_timeout(int sec)
{
    // ble_idle_set = sec * 1000;
    return 0;
}

void aiwb2_ble_wait_wifi_startup(void)
{
    // if (aiwb2.wifi.status == WIFI_DISCONNECTED)
    {
        // snprintf(at_send_buf, sizeof(at_send_buf), "AT+WMODE=0,0\r\n");
        // aiwb2_at_send(at_send_buf, "OK\r\n", wifi_set_mode_close_callback, 3, 3000);
        aiwb2.wifi.config_step = 0;
        wifi_config_callback(NULL);
        aiwb2.wifi.enable = 0;
        save_cfg(CFG_WIFI_EN, (void *)&(aiwb2.wifi.enable), sizeof(aiwb2.wifi.enable));
        aiwb2.ble.wait_wifi = 1;
    }
}

char *get_wireless_ver(void)
{
    return (char *)aiwb2.firmware_ver;
}

char *get_wifi_mac(void)
{
    return (char *)aiwb2.wifi.mac;
}

int aiwb2_wifi_set_real_time(char mode, char *host, int port, int delay, int upload, int alarm, int leave)
{
    aiwb2.wifi.real_time.mode = mode;
    aiwb2.wifi.real_time.delay = delay;
    aiwb2.wifi.real_time.upload = upload;
    aiwb2.wifi.real_time.alarm_delay = alarm;
    aiwb2.wifi.real_time.leave_delay = leave;
    if (aiwb2.wifi.real_time.mode == 0)
    {
        if (strlen(host) <= sizeof(aiwb2.wifi.socket.host))
        {
            // memcpy(aiwb2.wifi.socket.host, host, strlen(host));
            memcpy(aiwb2.wifi.real_time.socket.host, host, strlen(host));
        }
        else
        {
            return -1;
        }
        if (port > 0 && port <= 0xffff)
        {
            // aiwb2.wifi.socket.port = port;
            aiwb2.wifi.real_time.socket.port = port;
        }
        else
        {
            memset(aiwb2.wifi.socket.host, 0, sizeof(aiwb2.wifi.socket.host));
            memset(aiwb2.wifi.real_time.socket.host, 0, sizeof(aiwb2.wifi.real_time.socket.host));
            aiwb2.wifi.real_time.socket.port = 0;
            return -2;
        }
    }

    return 0;
}

int aiwb2_wifi_set_raw_upload(uint8_t enable, char *host, int port)
{
    if (enable > 0)
    {
        aiwb2.wifi.raw_upload.enable = 1;
        if (strlen(host) <= sizeof(aiwb2.wifi.socket.host))
        {
            memcpy(aiwb2.wifi.raw_upload.socket.host, host, strlen(host));
        }
        else
        {
            return -1;
        }
        if (port > 0 && port <= 0xffff)
        {
            aiwb2.wifi.raw_upload.socket.port = port;
        }
        else
        {
            memset(aiwb2.wifi.raw_upload.socket.host, 0, sizeof(aiwb2.wifi.raw_upload.socket.host));
            aiwb2.wifi.raw_upload.socket.port = 0;
            return -2;
        }
    }
    else if (enable == 0)
    {
        aiwb2.wifi.raw_upload.enable = 0;
        memset(aiwb2.wifi.raw_upload.socket.host, 0, sizeof(aiwb2.wifi.raw_upload.socket.host));
        aiwb2.wifi.raw_upload.socket.port = 0;
    }

    return 0;
}

int aiwb2_upgrade(char *host, int port, char *path)
{
    int use_default = 0;
    if (aiwb2.wifi.enable == 0)
    {
        return -1;
    }
    if (aiwb2.wifi.status != WIFI_CONNECTED)
    {
        return -2;
    }
    if (strlen(host) == 0 || strlen(path) == 0)
    {
        use_default = 1;
    }
    if (host == NULL || path == NULL)
    {
        use_default = 1;
    }
    if (use_default)
    {
        aiwb2_at_send("AT+OTA=1,\"aithinker111.oss-cn-beijing.aliyuncs.com\",80,\"/WB2_COMBO_2.13.1.xz\"\r\n", "OK\r\n", NULL, 3, 5000);
        aiwb2_at_send("AT+OTA\r\n", "OK\r\n", NULL, 3, 5000);
        aiwb2_start_upgrade = 1;
    }
    else
    {
        snprintf(at_send_buf, sizeof(at_send_buf), "AT+OTA=1,\"%s\",%d,\"%s\"\r\n", host, port, path);
        aiwb2_at_send(at_send_buf, "OK\r\n", NULL, 3, 3000);
        aiwb2_at_send("AT+OTA\r\n", "OK\r\n", NULL, 3, 5000);
        aiwb2_start_upgrade = 1;
    }

    return 0;
}

void aiwb2_get_network_status(uint8_t *wifi_conn, uint8_t *mqtt_conn, int *wifi_rssi)
{
    if (wifi_conn != NULL)
    {
        *wifi_conn = aiwb2.wifi.status;
    }
    if (mqtt_conn != NULL)
    {
        *mqtt_conn = aiwb2.wifi.mqtt.status;
    }
    if (wifi_rssi != NULL)
    {
        *wifi_rssi = aiwb2.wifi.rssi;
    }
}

/// @brief 设置产测
/// @param enable
void aiwb2_set_production(uint8_t enable)
{
    if (aiwb2.test.enable != enable)
    {
        memset(&(aiwb2.test), 0, sizeof(aiwb2.test));
        aiwb2.test.enable = enable;
    }
    aiwb2_init();
}

/// @brief 获取产测结果
/// @param
/// @return 1：测试成功
int aiwb2_get_production_result(void)
{
    return aiwb2.test.result;
}

/// @brief 设置产测结果标志
/// @param result 1：进入产测，0：退出产测
void aiwb2_set_production_result(int result)
{
    aiwb2.test.result = result;
}

int aiwb2_at(char *cmd, int noenter)
{
    printf("aiwb2 >>>>>>[%d]:%s\r\n", strlen((const char *)cmd), cmd);
    snprintf(at_send_buf, sizeof(at_send_buf), "%s\r\n", cmd);
    aiwb2_at_send(at_send_buf, "OK\r\n", NULL, 1, 10000);

    if (noenter != 1)
    {
        wifi_bt_uart_write("\r\n", 2);
    }

    return 0;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), at, aiwb2_at, at send);

int aiwb2_info(char *cmd, int noenter)
{
    printf("==== Network Module Information ====\r\n");
    printf("Module Status: %s\r\n", aiwb2.ready ? "Ready" : "Not Ready");
    printf("Firmware Version: %s\r\n", aiwb2.firmware_ver);
    printf("SDK Version: %s\r\n", aiwb2.sdk_ver);

    // Bluetooth information
    printf("\n--- Bluetooth Info ---\r\n");
    printf("MAC Address: %s\r\n", aiwb2.ble.mac);
    printf("Connection: %s\r\n", aiwb2.ble.status == BLE_CONNECTED ? "Connected" : "Disconnected");

    // WiFi information
    printf("\n--- WiFi Info ---\r\n");
    printf("Status: %s\r\n", aiwb2.wifi.enable ? "Enabled" : "Disabled");
    printf("MAC Address: %s\r\n", aiwb2.wifi.mac);
    printf("Connection: %s\r\n", aiwb2.wifi.status == WIFI_CONNECTED ? "Connected" : "Disconnected");
    if (aiwb2.wifi.status == WIFI_CONNECTED)
    {
        printf("IP Address: %s\r\n", aiwb2.wifi.ipv4);
        printf("Gateway: %s\r\n", aiwb2.wifi.gateway);
        printf("RSSI: %d\r\n", aiwb2.wifi.rssi);
    }

    // MQTT information
    printf("\n--- MQTT Info ---\r\n");
    printf("Connection: %s\r\n", aiwb2.wifi.mqtt.status == MQTT_CONNECTED ? "Connected" : "Disconnected");
    if (aiwb2.wifi.mqtt.status == MQTT_CONNECTED)
    {
        printf("Subscribe Topic: %s\r\n", aiwb2.wifi.mqtt.sub_topic);
        printf("Publish Topic: %s\r\n", aiwb2.wifi.mqtt.pub_topic);
        printf("Send Status: %d\r\n", aiwb2.wifi.mqtt.send_and_wait);
    }

    // Real-time transmission configuration
    printf("\n--- Real-time Transfer Config ---\r\n");
    printf("Mode: %d\r\n", aiwb2.wifi.real_time.mode);
    printf("Transfer Interval: %d sec\r\n", aiwb2.wifi.real_time.delay);
    printf("Data Upload: %s\r\n", aiwb2.wifi.real_time.upload ? "Enabled" : "Disabled");
    if (aiwb2.wifi.real_time.mode == 0 && strlen(aiwb2.wifi.socket.host) > 0)
    {
        printf("TCP Server: %s:%d\r\n", aiwb2.wifi.socket.host, aiwb2.wifi.socket.port);
    }

    // SNTP configuration
    printf("\n--- Time Sync ---\r\n");
    printf("SNTP Status: %s\r\n", aiwb2.wifi.sntp_ok ? "Synchronized" : "Not Synchronized");

    if (!noenter)
    {
        printf("\r\n");
    }

    return 0;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), network, aiwb2_info, network info);

int aiwb2_realtime(int enable)
{
    if(enable == 0)
    {
        // aiwb2_wifi_set_real_time(1, 
        //             NULL, 
        //             0, 
        //             120, 
        //             0, 
        //             0,
        //             0);
        real_time_enable(0);
    }
    else
    {
        aiwb2_wifi_set_real_time(1, 
                    NULL, 
                    0, 
                    120, 
                    0, 
                    0,
                    0);
        real_time_enable(REAL_TIME_WIFI);
    }
    return 0;
}

SHELL_EXPORT_CMD(SHELL_CMD_PERMISSION(0) | SHELL_CMD_TYPE(SHELL_TYPE_CMD_FUNC), realtime, aiwb2_realtime, realtime);
