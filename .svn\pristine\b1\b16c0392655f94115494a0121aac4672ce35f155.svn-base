#ifndef __BOARD_H__
#define __BOARD_H__

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stddef.h>
#ifdef AT32F403ACCT7
#include "at32f403a_407.h"
#include "at32f403a_407_clock.h"
#endif
#include <time.h>
#include "shell_port.h"
#include "shell.h"
#include "rtc.h"

#include "gpio_map.h"
#include "device_model.h"
//软件版本号
#define SOFTWARE_VER "1.3.2506241119"
//设备型号，没有颈椎温疗的是SNZT100，有颈椎温疗的是SNZT101
#define DEVICE_MODEL BAND_MODEL

#define BAND_PROJECT 1
#if BAND_PROJECT == 1
#define USE_FSR 0
#endif

#define MQTT_TEST 1

//是否启用WIFI自动上传
#define WIFI_AUTO_UPLOAD 1
//是否启用全部蓝牙功能
#define BLE_ALL_FUNC 0

//是否启用蜂鸣器，调试可以关掉
#define USE_BEEP 1
//是否启用气泵
#define USE_PUMP 0

//是否启用授权
#define AUTH_ENABLE 1
//是否启用授权后才生成记录
#if AUTH_ENABLE == 1
#define AUTH_RECORD 1
#endif

//快速生成睡眠记录，调试用
#define DBG_FAST_SLEEP 0
//是否采用高采样率
#define USE_HIGH_ADC_SAMPLE 0
//实时数据测试
#define REAL_TIME_TEST 0
//自动生成记录
#define RECORD_TEST 0
//升级报文是否需要蓝牙授权
#define UPGRADE_NOT_AUTH 1
//蓝牙是否需要授权
#define BLE_NOT_AUTH 0
//石墨烯加热调试
#define GRAPHENE_HEAT_DEBUG 0
//是否启用串口打印波形数据
#define ADC_UART_DEBUG 1

//是否启用新的睡眠周期计算
#define NEW_SLEEP_STATUS 1
//是否启用软件滤波
#define SOFTWARE_FILETE 1
#if SOFTWARE_FILETE == 1
#if USE_HIGH_ADC_SAMPLE == 1
#define USE_HRV_CALC 0
#define USE_NEW_HEARTBEAT 0
#elif USE_HIGH_ADC_SAMPLE == 0
#define USE_HRV_CALC 1
#define USE_NEW_HEARTBEAT 1
#endif
//硬件基准电压
#define BASE_VOLTAGE 1.5f
#endif

//是否启用记录指针
#define USE_RECORD_POINT 0
//记录保存最长天数
#define RECORD_SAVE_DAYS 15

//蓝牙广播名字前缀
#define BLE_SN_NAME_PREFIX "SN-ZT_"
#define BLE_ZT_TEST_NAME_PREFIX "ZT-TEST_"
#define BLE_NEW_SN_NAME_PREFIX "Pillow-ZS_"
#define BLE_ADV_NAME_PREFIX BLE_NEW_SN_NAME_PREFIX
#define WIFI_ENABLE 0
//WIFI产测SSID
#define WIFI_PRODUCTION_SSID "亟智科技666"
//获取结构体成员大小
#define MEMBER_SIZE(type, member) sizeof(((type *)0)->member)
//获取结构体成员地址便宜
#define MEMBER_ADDRESS(type, member) (uint32_t)(&(((type *)0)->member))
//获取数组列数
#ifndef ARRAY_SIZE
#define ARRAY_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))
#endif
//清空数组
#define ARRAY_CLEAR(arr, x) memset(arr, x, sizeof(arr))
//设置ms延时
#define SET_MS_DELAY(x)    (get_tick() + x)
//设置秒延时
#define SET_SEC_DELAY(x)   (get_tick() + (x) * 1000)
//判断是否超时
#define IS_TIMEOUT(x)      (get_tick() > x)

#define BCD2BIN(n) (((((n) >> 4) & 0x0F) * 10) + ((n)&0x0F))
#define BIN2BCD(n) ((((n) / 10) << 4) | ((n) % 10))

#define APP_ASSERT(EXPR)                                                      \
if (!(EXPR))                                                             \
{                                                                             \
    printf("(%s) has assert failed at %s.\n", #EXPR, __func__);             \
    while (1);                                                                \
}

#if 0
#define alloc_printf(...)  printf(__VA_ARGS__)
#else
#define alloc_printf(...)  
#endif

void uart_print_init(uint32_t baudrate);

void uart_adc_debug_init(uint32_t baudrate);

void uart_adc_debug_output(uint16_t raw, uint16_t rvol, uint16_t hvol, uint16_t bvol, uint16_t svol);

void uart_adc_debug_output_float(double hvol, double hvol2, double hvol3);

void wdt_init(void);

void feed_wdt(void);

void inc_tick(void);

uint64_t get_tick(void);

void delay_ms(uint32_t ms);

#endif
