Dependencies for Project 'project', Target 'project': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\bsp\src\board.c)(0x6822ACBA)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\board.o --omf_browse .\objects\board.crf --depend .\objects\board.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\bsp\src\main.c)(0x6853660E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\calc_method.h)(0x67DA7BDB)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\graphene.h)(0x674EBE1D)
I (..\..\..\app\inc\ble_protocol.h)(0x67DA7C0B)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
I (..\..\..\app\inc\beep.h)(0x67E4C8C1)
I (..\..\..\app\inc\adc_fileter.h)(0x6850D2B7)
I (..\..\..\app\inc\startup_count.h)(0x6613DE50)
I (..\..\..\app\inc\production_test.h)(0x67DA79B9)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_const_structs.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_common_tables.h)(0x6778E708)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
F (..\..\..\app\src\adc.c)(0x681C5675)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\adc.o --omf_browse .\objects\adc.crf --depend .\objects\adc.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\main.h)(0x685365E4)
F (..\..\..\app\src\adc_fileter.c)(0x684BE58A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\adc_fileter.o --omf_browse .\objects\adc_fileter.crf --depend .\objects\adc_fileter.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_fileter.h)(0x6850D2B7)
F (..\..\..\app\src\ai_wb2.c)(0x685B5DB0)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\ai_wb2.o --omf_browse .\objects\ai_wb2.crf --depend .\objects\ai_wb2.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\ble_protocol.h)(0x67DA7C0B)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\wifi_protocol.h)(0x681194B7)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\wifi_bt_uart.h)(0x666AB530)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
I (..\..\..\app\inc\beep.h)(0x67E4C8C1)
I (..\..\..\third_party\AT-Command\at_chat.h)(0x6613DE50)
I (..\..\..\third_party\AT-Command\list.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\reset_flag.h)(0x68522E79)
F (..\..\..\app\src\air_pump.c)(0x6854F6DC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\air_pump.o --omf_browse .\objects\air_pump.crf --depend .\objects\air_pump.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\calc_method.h)(0x67DA7BDB)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\at32f403a_407_clock.c)(0x67E0FD62)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_clock.o --omf_browse .\objects\at32f403a_407_clock.crf --depend .\objects\at32f403a_407_clock.d)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\app\src\at32f403a_407_int.c)(0x6613DE50)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_int.o --omf_browse .\objects\at32f403a_407_int.crf --depend .\objects\at32f403a_407_int.d)
I (..\..\..\app\inc\at32f403a_407_int.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\app\src\authorize.c)(0x6854B69C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\authorize.o --omf_browse .\objects\authorize.crf --depend .\objects\authorize.d)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\utils_md5.h)(0x6613DE50)
F (..\..\..\app\src\beep.c)(0x67E4D00E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\beep.o --omf_browse .\objects\beep.crf --depend .\objects\beep.d)
I (..\..\..\app\inc\beep.h)(0x67E4C8C1)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\app\src\ble_protocol.c)(0x6830831C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\ble_protocol.o --omf_browse .\objects\ble_protocol.crf --depend .\objects\ble_protocol.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\utils_base64.h)(0x6613DE50)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\ble_protocol.h)(0x67DA7C0B)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\graphene.h)(0x674EBE1D)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
F (..\..\..\app\src\calc_method.c)(0x6800A16A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\calc_method.o --omf_browse .\objects\calc_method.crf --depend .\objects\calc_method.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\calc_method.h)(0x67DA7BDB)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\config.c)(0x685A1922)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\config.o --omf_browse .\objects\config.crf --depend .\objects\config.d)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\app\inc\utils_md5.h)(0x6613DE50)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
F (..\..\..\app\src\graphene.c)(0x6808A17C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\graphene.o --omf_browse .\objects\graphene.crf --depend .\objects\graphene.d)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\graphene.h)(0x674EBE1D)
I (..\..\..\app\inc\beep.h)(0x67E4C8C1)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\opa_gear.c)(0x684150A1)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\opa_gear.o --omf_browse .\objects\opa_gear.crf --depend .\objects\opa_gear.d)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\config.h)(0x6809D822)
F (..\..\..\app\src\ota.c)(0x67F49BC5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\ota.o --omf_browse .\objects\ota.crf --depend .\objects\ota.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\app\inc\utils_md5.h)(0x6613DE50)
F (..\..\..\app\src\production_test.c)(0x682E8ED2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\production_test.o --omf_browse .\objects\production_test.crf --depend .\objects\production_test.d)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\beep.h)(0x67E4C8C1)
I (..\..\..\app\inc\production_test.h)(0x67DA79B9)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\graphene.h)(0x674EBE1D)
F (..\..\..\app\src\record.c)(0x6819C7D9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\record.o --omf_browse .\objects\record.crf --depend .\objects\record.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
F (..\..\..\app\src\rtc.c)(0x67BC2FF9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\rtc.o --omf_browse .\objects\rtc.crf --depend .\objects\rtc.d)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\config.h)(0x6809D822)
F (..\..\..\app\src\startup_count.c)(0x67FC577A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\startup_count.o --omf_browse .\objects\startup_count.crf --depend .\objects\startup_count.d)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\app\inc\startup_count.h)(0x6613DE50)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\app\src\utils_base64.c)(0x6613DE50)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\utils_base64.o --omf_browse .\objects\utils_base64.crf --depend .\objects\utils_base64.d)
I (..\..\..\app\inc\utils_base64.h)(0x6613DE50)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\app\src\utils_md5.c)(0x6613DE50)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\utils_md5.o --omf_browse .\objects\utils_md5.crf --depend .\objects\utils_md5.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\app\inc\utils_md5.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
F (..\..\..\app\src\wifi_bt_uart.c)(0x67CBDB54)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\wifi_bt_uart.o --omf_browse .\objects\wifi_bt_uart.crf --depend .\objects\wifi_bt_uart.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (..\..\..\app\inc\wifi_bt_uart.h)(0x666AB530)
F (..\..\..\app\src\wifi_protocol.c)(0x683FB4CA)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\wifi_protocol.o --omf_browse .\objects\wifi_protocol.crf --depend .\objects\wifi_protocol.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\utils_base64.h)(0x6613DE50)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\wifi_protocol.h)(0x681194B7)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
F (..\..\..\app\src\real_time.c)(0x68119590)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\real_time.o --omf_browse .\objects\real_time.crf --depend .\objects\real_time.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\ble_protocol.h)(0x67DA7C0B)
I (..\..\..\app\inc\ota.h)(0x67EB9C05)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
F (..\..\..\app\src\adc_collection.c)(0x684B9D7B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\adc_collection.o --omf_browse .\objects\adc_collection.crf --depend .\objects\adc_collection.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_fileter.h)(0x6850D2B7)
I (..\..\..\app\inc\air_pump.h)(0x682E97E4)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_const_structs.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_common_tables.h)(0x6778E708)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\real_time.h)(0x67DA7C46)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
I (..\..\..\app\inc\calc_method.h)(0x67DA7BDB)
I (..\..\..\app\inc\graphene.h)(0x674EBE1D)
I (..\..\..\app\inc\ai_wb2.h)(0x6850D39A)
F (..\..\..\app\src\reset_flag.c)(0x685232DA)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\reset_flag.o --omf_browse .\objects\reset_flag.crf --depend .\objects\reset_flag.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\reset_flag.h)(0x68522E79)
F (..\..\..\app\src\sleep_method\body_energy.c)(0x683FF234)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\body_energy.o --omf_browse .\objects\body_energy.crf --depend .\objects\body_energy.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\sleep_method\cat_heartbeat.c)(0x6819C2E4)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\cat_heartbeat.o --omf_browse .\objects\cat_heartbeat.crf --depend .\objects\cat_heartbeat.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\config.h)(0x6809D822)
F (..\..\..\app\src\sleep_method\dog_heartbeat.c)(0x684BE5A9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\dog_heartbeat.o --omf_browse .\objects\dog_heartbeat.crf --depend .\objects\dog_heartbeat.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\config.h)(0x6809D822)
F (..\..\..\app\src\sleep_method\heartbeat.c)(0x6827E1C5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\heartbeat.o --omf_browse .\objects\heartbeat.crf --depend .\objects\heartbeat.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\config.h)(0x6809D822)
F (..\..\..\app\src\sleep_method\heartbeat_method.c)(0x67DA8C97)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\heartbeat_method.o --omf_browse .\objects\heartbeat_method.crf --depend .\objects\heartbeat_method.d)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
F (..\..\..\app\src\sleep_method\sleep_record.c)(0x6827E79A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\sleep_record.o --omf_browse .\objects\sleep_record.crf --depend .\objects\sleep_record.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\app\inc\authorize.h)(0x67DB72F9)
I (..\..\..\app\inc\opa_gear.h)(0x681C5E36)
F (..\..\..\app\src\sleep_method\sleep_report.c)(0x68059737)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\sleep_report.o --omf_browse .\objects\sleep_report.crf --depend .\objects\sleep_report.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\sleep_method\sleep_save.c)(0x67DB6D74)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\sleep_save.o --omf_browse .\objects\sleep_save.crf --depend .\objects\sleep_save.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
F (..\..\..\app\src\sleep_method\sleep_status.c)(0x684A236A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\sleep_status.o --omf_browse .\objects\sleep_status.crf --depend .\objects\sleep_status.d)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\app\src\sleep_method\snore.c)(0x6806E1D5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\snore.o --omf_browse .\objects\snore.crf --depend .\objects\snore.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\app\inc\config.h)(0x6809D822)
I (..\..\..\app\inc\adc.h)(0x6850D7EE)
I (..\bsp\inc\main.h)(0x685365E4)
I (..\..\..\app\inc\sleep_method\snore.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_method.h)(0x6850D1BA)
I (..\..\..\app\inc\sleep_method\heartbeat_method.h)(0x6850D635)
I (..\..\..\app\inc\adc_collection.h)(0x6850D38D)
I (..\..\..\app\inc\sleep_method\heartbeat.h)(0x6850D227)
I (..\..\..\app\inc\sleep_method\dog_heartbeat.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_record.h)(0x6850D2B7)
I (..\..\..\app\inc\record.h)(0x681B22CE)
I (..\..\..\app\inc\flashdb_def.h)(0x67DBA71C)
I (..\..\..\app\inc\sleep_method\sleep_save.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\sleep_status.h)(0x6850D2B7)
I (..\..\..\app\inc\sleep_method\body_energy.h)(0x6850D273)
I (..\..\..\app\inc\sleep_method\sleep_report.h)(0x6850D2B7)
F (..\..\..\third_party\xqueue\src\xqueue.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\xqueue.o --omf_browse .\objects\xqueue.crf --depend .\objects\xqueue.d)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\third_party\FlashDB-2.0.0\src\fdb.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fdb.o --omf_browse .\objects\fdb.crf --depend .\objects\fdb.d)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_low_lvl.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x6025237E)
F (..\..\..\third_party\FlashDB-2.0.0\src\fdb_kvdb.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fdb_kvdb.o --omf_browse .\objects\fdb_kvdb.crf --depend .\objects\fdb_kvdb.d)
I (C:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_low_lvl.h)(0x6613DE50)
F (..\..\..\third_party\FlashDB-2.0.0\src\fdb_tsdb.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fdb_tsdb.o --omf_browse .\objects\fdb_tsdb.crf --depend .\objects\fdb_tsdb.d)
I (C:\Keil_v5\ARM\ARMCC\include\inttypes.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_low_lvl.h)(0x6613DE50)
F (..\..\..\third_party\FlashDB-2.0.0\src\fdb_utils.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fdb_utils.o --omf_browse .\objects\fdb_utils.crf --depend .\objects\fdb_utils.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\third_party\FlashDB-2.0.0\inc\flashdb.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_cfg.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_def.h)(0x6613DE50)
I (..\..\..\third_party\FlashDB-2.0.0\inc\fdb_low_lvl.h)(0x6613DE50)
F (..\..\..\third_party\FlashDB-2.0.0\port\fal\src\fal.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fal.o --omf_browse .\objects\fal.crf --depend .\objects\fal.d)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\..\..\third_party\FlashDB-2.0.0\port\fal\src\fal_flash.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fal_flash.o --omf_browse .\objects\fal_flash.crf --depend .\objects\fal_flash.d)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
F (..\..\..\third_party\FlashDB-2.0.0\port\fal\src\fal_flash_at32f413_port.c)(0x67FF476B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fal_flash_at32f413_port.o --omf_browse .\objects\fal_flash_at32f413_port.crf --depend .\objects\fal_flash_at32f413_port.d)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
F (..\..\..\third_party\FlashDB-2.0.0\port\fal\src\fal_partition.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fal_partition.o --omf_browse .\objects\fal_partition.crf --depend .\objects\fal_partition.d)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal.h)(0x6613DE51)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_cfg.h)(0x67DBAA43)
I (..\..\..\third_party\FlashDB-2.0.0\port\fal\inc\fal_def.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
F (..\..\..\third_party\cJSON-1.7.16\cJSON.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\cjson.o --omf_browse .\objects\cjson.crf --depend .\objects\cjson.d)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (C:\Keil_v5\ARM\ARMCC\include\ctype.h)(0x60252376)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (..\..\..\third_party\cJSON-1.7.16\cJSON.h)(0x6613DE51)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
F (..\..\..\third_party\AT-Command\at_chat.c)(0x6773594B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at_chat.o --omf_browse .\objects\at_chat.crf --depend .\objects\at_chat.d)
I (..\..\..\third_party\AT-Command\at_chat.h)(0x6613DE50)
I (..\..\..\third_party\AT-Command\list.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (C:\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
F (..\..\..\third_party\letter-shell-3.1.2\shell.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\shell.o --omf_browse .\objects\shell.crf --depend .\objects\shell.d)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (..\..\..\third_party\letter-shell-3.1.2\shell_ext.h)(0x6613DE51)
F (..\..\..\third_party\letter-shell-3.1.2\shell_cmd_list.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\shell_cmd_list.o --omf_browse .\objects\shell_cmd_list.crf --depend .\objects\shell_cmd_list.d)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
F (..\..\..\third_party\letter-shell-3.1.2\shell_companion.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\shell_companion.o --omf_browse .\objects\shell_companion.crf --depend .\objects\shell_companion.d)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
F (..\..\..\third_party\letter-shell-3.1.2\shell_ext.c)(0x6613DE51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\shell_ext.o --omf_browse .\objects\shell_ext.crf --depend .\objects\shell_ext.d)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_ext.h)(0x6613DE51)
F (..\..\..\third_party\letter-shell-3.1.2\shell_port.c)(0x673FD7A3)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\shell_port.o --omf_browse .\objects\shell_port.crf --depend .\objects\shell_port.d)
I (..\..\..\third_party\letter-shell-3.1.2\shell.h)(0x6613DE51)
I (..\..\..\third_party\letter-shell-3.1.2\shell_cfg.h)(0x673FD280)
I (..\bsp\inc\board.h)(0x685366A6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (C:\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_clock.h)(0x6613DE50)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\..\..\third_party\letter-shell-3.1.2\shell_port.h)(0x6613DE51)
I (..\..\..\app\inc\rtc.h)(0x67440CB2)
I (..\bsp\inc\gpio_map.h)(0x68073AD4)
I (..\..\..\app\inc\device_model.h)(0x682D90D1)
I (..\..\..\third_party\xqueue\inc\xqueue.h)(0x6613DE51)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_acc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_acc.o --omf_browse .\objects\at32f403a_407_acc.crf --depend .\objects\at32f403a_407_acc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_adc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_adc.o --omf_browse .\objects\at32f403a_407_adc.crf --depend .\objects\at32f403a_407_adc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_bpr.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_bpr.o --omf_browse .\objects\at32f403a_407_bpr.crf --depend .\objects\at32f403a_407_bpr.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_can.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_can.o --omf_browse .\objects\at32f403a_407_can.crf --depend .\objects\at32f403a_407_can.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_crc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_crc.o --omf_browse .\objects\at32f403a_407_crc.crf --depend .\objects\at32f403a_407_crc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_crm.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_crm.o --omf_browse .\objects\at32f403a_407_crm.crf --depend .\objects\at32f403a_407_crm.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_dac.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_dac.o --omf_browse .\objects\at32f403a_407_dac.crf --depend .\objects\at32f403a_407_dac.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_debug.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_debug.o --omf_browse .\objects\at32f403a_407_debug.crf --depend .\objects\at32f403a_407_debug.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_dma.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_dma.o --omf_browse .\objects\at32f403a_407_dma.crf --depend .\objects\at32f403a_407_dma.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_emac.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_emac.o --omf_browse .\objects\at32f403a_407_emac.crf --depend .\objects\at32f403a_407_emac.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_exint.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_exint.o --omf_browse .\objects\at32f403a_407_exint.crf --depend .\objects\at32f403a_407_exint.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_flash.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_flash.o --omf_browse .\objects\at32f403a_407_flash.crf --depend .\objects\at32f403a_407_flash.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_gpio.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_gpio.o --omf_browse .\objects\at32f403a_407_gpio.crf --depend .\objects\at32f403a_407_gpio.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_i2c.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_i2c.o --omf_browse .\objects\at32f403a_407_i2c.crf --depend .\objects\at32f403a_407_i2c.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_misc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_misc.o --omf_browse .\objects\at32f403a_407_misc.crf --depend .\objects\at32f403a_407_misc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_pwc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_pwc.o --omf_browse .\objects\at32f403a_407_pwc.crf --depend .\objects\at32f403a_407_pwc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_rtc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_rtc.o --omf_browse .\objects\at32f403a_407_rtc.crf --depend .\objects\at32f403a_407_rtc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_sdio.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_sdio.o --omf_browse .\objects\at32f403a_407_sdio.crf --depend .\objects\at32f403a_407_sdio.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_spi.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_spi.o --omf_browse .\objects\at32f403a_407_spi.crf --depend .\objects\at32f403a_407_spi.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_tmr.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_tmr.o --omf_browse .\objects\at32f403a_407_tmr.crf --depend .\objects\at32f403a_407_tmr.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_usart.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_usart.o --omf_browse .\objects\at32f403a_407_usart.crf --depend .\objects\at32f403a_407_usart.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_usb.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_usb.o --omf_browse .\objects\at32f403a_407_usb.crf --depend .\objects\at32f403a_407_usb.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_wdt.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_wdt.o --omf_browse .\objects\at32f403a_407_wdt.crf --depend .\objects\at32f403a_407_wdt.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_wwdt.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_wwdt.o --omf_browse .\objects\at32f403a_407_wwdt.crf --depend .\objects\at32f403a_407_wwdt.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\drivers\src\at32f403a_407_xmc.c)(0x653A6203)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\at32f403a_407_xmc.o --omf_browse .\objects\at32f403a_407_xmc.crf --depend .\objects\at32f403a_407_xmc.d)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.c)(0x653A620C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\system_at32f403a_407.o --omf_browse .\objects\system_at32f403a_407.crf --depend .\objects\system_at32f403a_407.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\at32f403a_407.h)(0x653A6388)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\core_cm4.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_version.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\mpu_armv7.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\system_at32f403a_407.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_def.h)(0x653A6203)
I (..\..\..\app\inc\at32f403a_407_conf.h)(0x6613DE50)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crm.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_tmr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_rtc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_bpr.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_gpio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_i2c.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usart.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_pwc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_can.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_adc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dac.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_spi.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_dma.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_debug.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_flash.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_crc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wwdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_wdt.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_exint.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_sdio.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_xmc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_acc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_misc.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_usb.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\drivers\inc\at32f403a_407_emac.h)(0x653A6203)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support\startup\mdk\startup_at32f403a_407.s)(0x6639C438)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 538" --pd "AT32F403ACCT7 SETA 1"

--list .\listings\startup_at32f403a_407.lst --xref -o .\objects\startup_at32f403a_407.o --depend .\objects\startup_at32f403a_407.d)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\BasicMathFunctions.c)(0x653A6204)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\basicmathfunctions.o --omf_browse .\objects\basicmathfunctions.crf --depend .\objects\basicmathfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_abs_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_abs_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_abs_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_abs_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_add_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_add_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_add_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_add_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_and_u16.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_and_u32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_and_u8.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_dot_prod_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_dot_prod_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_dot_prod_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_dot_prod_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_mult_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_mult_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_mult_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_mult_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_negate_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_negate_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_negate_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_negate_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_not_u16.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_not_u32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_not_u8.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_offset_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_offset_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_offset_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_offset_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_or_u16.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_or_u32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_or_u8.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_scale_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_scale_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_scale_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_scale_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_shift_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_shift_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_shift_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_sub_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_sub_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_sub_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_sub_q7.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_xor_u16.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_xor_u32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BasicMathFunctions\arm_xor_u8.c)(0x653A6204)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BayesFunctions\BayesFunctions.c)(0x653A6207)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\bayesfunctions.o --omf_browse .\objects\bayesfunctions.crf --depend .\objects\bayesfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\BayesFunctions\arm_gaussian_naive_bayes_predict_f32.c)(0x653A6207)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\CommonTables\CommonTables.c)(0x653A6208)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\commontables.o --omf_browse .\objects\commontables.crf --depend .\objects\commontables.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\CommonTables\arm_common_tables.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_common_tables.h)(0x6778E708)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\CommonTables\arm_const_structs.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_const_structs.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\CommonTables\arm_mve_tables.c)(0x653A6208)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\ComplexMathFunctions.c)(0x653A6208)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\complexmathfunctions.o --omf_browse .\objects\complexmathfunctions.crf --depend .\objects\complexmathfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_conj_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_conj_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_conj_q31.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_dot_prod_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_dot_prod_q31.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_q31.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_squared_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mag_squared_q31.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_cmplx_q31.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_real_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_real_q15.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\ComplexMathFunctions\arm_cmplx_mult_real_q31.c)(0x653A6208)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\DistanceFunctions.c)(0x653A6208)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\distancefunctions.o --omf_browse .\objects\distancefunctions.crf --depend .\objects\distancefunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_boolean_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_boolean_distance_template.h)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_braycurtis_distance_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_canberra_distance_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_chebyshev_distance_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_cityblock_distance_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_correlation_distance_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_cosine_distance_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_dice_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_euclidean_distance_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_hamming_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_jaccard_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_jensenshannon_distance_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_kulsinski_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_minkowski_distance_f32.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_rogerstanimoto_distance.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_russellrao_distance.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_sokalmichener_distance.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_sokalsneath_distance.c)(0x653A6208)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\DistanceFunctions\arm_yule_distance.c)(0x653A6209)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\FastMathFunctions.c)(0x67775666)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\fastmathfunctions.o --omf_browse .\objects\fastmathfunctions.crf --depend .\objects\fastmathfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\arm_cos_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_common_tables.h)(0x6778E708)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\arm_sqrt_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\arm_sqrt_q31.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\arm_vexp_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FastMathFunctions\arm_vlog_f32.c)(0x653A6209)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\FilteringFunctions.c)(0x6760D58B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\filteringfunctions.o --omf_browse .\objects\filteringfunctions.crf --depend .\objects\filteringfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df1_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df2T_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df2T_f64.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f64.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_init_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_fast_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_fast_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_fast_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_opt_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_fast_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_fast_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_fast_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_opt_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_partial_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_conv_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_fast_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_fast_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_fast_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_opt_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_opt_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_correlate_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_fast_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_fast_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_init_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_init_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_init_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_decimate_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_fast_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_fast_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_init_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_init_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_init_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_init_q7.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_init_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_init_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_interpolate_q31.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_init_f32.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_init_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_lattice_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_q7.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_init_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_init_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_init_q7.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_fir_sparse_q7.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_init_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_init_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_iir_lattice_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_init_f32.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_init_q15.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_init_q31.c)(0x653A620A)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_q15.c)(0x653A620B)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\FilteringFunctions\arm_lms_q31.c)(0x653A620A)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\MatrixFunctions.c)(0x653A6205)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\matrixfunctions.o --omf_browse .\objects\matrixfunctions.crf --depend .\objects\matrixfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_add_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_add_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_add_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_cmplx_mult_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_cmplx_mult_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_cmplx_mult_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_init_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_init_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_init_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_inverse_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_inverse_f64.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_mult_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_mult_fast_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_mult_fast_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_mult_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_mult_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_scale_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_scale_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_scale_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_sub_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_sub_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_sub_q31.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_trans_f32.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_trans_q15.c)(0x653A6204)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\MatrixFunctions\arm_mat_trans_q31.c)(0x653A6205)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\StatisticsFunctions.c)(0x653A6205)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\statisticsfunctions.o --omf_browse .\objects\statisticsfunctions.crf --depend .\objects\statisticsfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_entropy_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_entropy_f64.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_kullback_leibler_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_kullback_leibler_f64.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_logsumexp_dot_prod_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_logsumexp_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_max_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_max_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_max_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_max_q7.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_max_no_idx_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_mean_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_mean_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_mean_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_mean_q7.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_min_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_min_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_min_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_min_q7.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_power_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_power_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_power_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_power_q7.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_rms_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_rms_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_rms_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_std_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_std_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_std_q31.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_var_f32.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_var_q15.c)(0x653A6205)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\StatisticsFunctions\arm_var_q31.c)(0x653A6205)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\SupportFunctions.c)(0x675FCF6E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\supportfunctions.o --omf_browse .\objects\supportfunctions.crf --depend .\objects\supportfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_barycenter_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_bitonic_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude\arm_sorting.h)(0x653A6203)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_bubble_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_copy_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_copy_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_copy_q31.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_copy_q7.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_fill_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_fill_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_fill_q31.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_fill_q7.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_heap_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_insertion_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_merge_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_merge_sort_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_quick_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_selection_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_sort_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_sort_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_spline_interp_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_spline_interp_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_weighted_sum_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_float_to_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_float_to_q31.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_float_to_q7.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q15_to_float.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q15_to_q31.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q15_to_q7.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q31_to_float.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q31_to_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q31_to_q7.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q7_to_float.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q7_to_q15.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SupportFunctions\arm_q7_to_q31.c)(0x653A6209)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\SVMFunctions.c)(0x653A6209)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\svmfunctions.o --omf_browse .\objects\svmfunctions.crf --depend .\objects\svmfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_linear_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_linear_predict_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_polynomial_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_polynomial_predict_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_rbf_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_rbf_predict_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_sigmoid_init_f32.c)(0x653A6209)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\SVMFunctions\arm_svm_sigmoid_predict_f32.c)(0x653A6209)
F (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\TransformFunctions.c)(0x6760D52E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\..\..\at32f403a_libraries_v2.1.6\drivers\inc -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\device_support -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\include -I ..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\PrivateInclude -I ..\..\..\third_party\FlashDB-2.0.0\inc -I ..\..\..\third_party\FlashDB-2.0.0\port\fal\inc -I ..\..\..\third_party\cJSON-1.7.16 -I ..\..\..\third_party\letter-shell-3.1.2 -I ..\..\..\third_party\AT-Command -I ..\..\..\third_party\xqueue\inc -I ..\..\..\app\inc -I ..\..\..\app\inc\sleep_method -I ..\bsp\inc

-IC:\Users\<USER>\AppData\Local\Arm\Packs\ArteryTek\AT32F403A_407_DFP\2.2.1\Device\Include

-IC:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="538" -DAT32F403ACCT7 -DAT32F403ACCT7 -DUSE_STDPERIPH_DRIVER

-o .\objects\transformfunctions.o --omf_browse .\objects\transformfunctions.crf --depend .\objects\transformfunctions.d)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_bitreversal.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_math.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_compiler.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\cmsis_armcc.h)(0x653A620C)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x6025237E)
I (C:\Keil_v5\ARM\ARMCC\include\limits.h)(0x60252376)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_common_tables.h)(0x6778E708)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_bitreversal2.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_cfft_f32.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_cfft_init_f32.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\cm4\core_support\arm_const_structs.h)(0x653A620C)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_cfft_radix8_f32.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_rfft_fast_f32.c)(0x653A6206)
I (..\..\..\at32f403a_libraries_v2.1.6\cmsis\dsp\Source\TransformFunctions\arm_rfft_fast_init_f32.c)(0x653A6206)
